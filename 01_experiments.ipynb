from langchain_community.vectorstores import Chroma
from langchain.chains import RetrievalQ<PERSON>
from langchain_openai import ChatOpenAI
import requests
import os
from typing import List
from dotenv import load_dotenv
load_dotenv()


import requests
from typing import List

class EuriAIEmbeddings:
    def __init__(self, api_key: str, model: str = "text-embedding-3-small"):
        self.api_key = api_key
        self.model = model
        self.url = "https://api.euron.one/api/v1/euri/embeddings" # EuriAI Endpoint

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        embeddings = []
        # It's good practice to batch requests if you have many texts,
        # but for simplicity, we'll embed one by one here.
        # You might want to optimize this for performance later.
        for text in texts:
            payload = {
                "input": text,
                "model": self.model
            }
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            try:
                response = requests.post(self.url, json=payload, headers=headers)
                response.raise_for_status() # Raises an HTTPError for bad responses (4xx or 5xx)
                # Extract the embedding vector from the response
                # Adjust the key access based on the actual EuriAI API response structure
                embedding = response.json()["data"][0]["embedding"]
                embeddings.append(embedding)
            except requests.exceptions.RequestException as e:
                print(f"Error getting embedding for text: {text[:30]}... Error: {e}")
                raise # Re-raise the exception to stop the process
            except KeyError as e:
                print(f"Error parsing embedding response: {e}. Response: {response.text}")
                raise
        return embeddings

    def embed_query(self, text: str) -> List[float]:
        # Embed a single query string
        return self.embed_documents([text])[0]

# 2. Load your EuriAI API Key (ensure it's in your .env file)
from dotenv import load_dotenv
import os

load_dotenv()
EURI_API_KEY = os.getenv("EURI_API_KEY") # Make sure .env has EURI_API_KEY=your_actual_key



from langchain_community.document_loaders import UnstructuredHTMLLoader

# Create a document loader for unstructured HTML
loader = UnstructuredHTMLLoader("datacamp-blog.html")

# Load the document
data = loader.load()

# Print the first document's content
print(data[0].page_content)

# Print the first document's metadata
print(data[0].metadata)

from langchain_text_splitters import CharacterTextSplitter

text = '''RAG (retrieval augmented generation) is an advanced NLP model that combines retrieval mechanisms with generative capabilities. RAG aims to improve the accuracy and relevance of its outputs by grounding responses in precise, contextually appropriate data.'''

# Define a character-based text splitter
text_splitter = CharacterTextSplitter(
    separator='.',  # split by period
    chunk_size=75,
    chunk_overlap=10
)

chunks = text_splitter.split_text(text)
print(chunks)
print([len(chunk) for chunk in chunks])


from langchain_community.document_loaders import PyPDFLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter

# Load the PDF
loader = PyPDFLoader("Living-Goods-Q3-2023-Stakeholder-Report_Web.pdf")
documents = loader.load()

# Define the text splitter with the correct separators
text_splitter = RecursiveCharacterTextSplitter(
    separators=["\n", ".", " ", ""],  # <- Exactly as required
    chunk_size=75,
    chunk_overlap=10
)

# Split the document
chunks = text_splitter.split_documents(documents)

# Print results
print([chunk.page_content for chunk in chunks])
print([len(chunk.page_content) for chunk in chunks])


from langchain_openai import OpenAIEmbeddings
from langchain_community.vectorstores import Chroma

# --- 3. Initialize the EuriAI embedding model (REPLACES OpenAIEmbeddings) ---
embedding_model = EuriAIEmbeddings(api_key=EURI_API_KEY, model='text-embedding-3-small')

# --- 4. Create a Chroma vector store and embed the chunks ---
vector_store = Chroma.from_documents(documents=chunks, embedding=embedding_model)


prompt = """
Use the only the context provided to answer the following question. If you don't know the answer, reply that you are unsure.
Context: {context}
Question: {question}
"""

# Convert the string into a chat prompt template
prompt_template = ChatPromptTemplate.from_template(prompt)

# Create an LCEL chain to test the prompt
chain = prompt_template | llm

# Invoke the chain on the inputs provided
print(chain.invoke({"context": "DataCamp's RAG course was created by Meri Nova and James Chapman!", "question": "Who created DataCamp's RAG course?"}))

from langchain_core.output_parsers import StrOutputParser

# Step 1: Convert the vector store into a retriever
retriever = vector_store.as_retriever(
    search_type="similarity",
    search_kwargs={"k": 4}  # you can adjust `k` as needed
)

# Step 2: Create the LCEL retrieval chain
chain = (
    {"context": retriever, "question": lambda x: x}
    | prompt_template
    | llm
    | StrOutputParser()
)

# Step 3: Invoke the chain
print(chain.invoke("Who are the authors?"))


from langchain.document_loaders import UnstructuredMarkdownLoader

# ✅ Create a document loader for README.md and load it
loader = UnstructuredMarkdownLoader("README.md")

markdown_data = loader.load()
print(markdown_data[0])


from langchain_community.document_loaders import PythonLoader

# Create a document loader for rag.py and load it
loader = PythonLoader(r"C:\Euron\finsolve-rag-rbac-assistant\src\api\main.py")

python_data = loader.load()
print(python_data[0])


from langchain_text_splitters import RecursiveCharacterTextSplitter, Language

# ✅ Create a Python-aware recursive character splitter 
python_splitter = RecursiveCharacterTextSplitter.from_language(
    language=Language.PYTHON,
    chunk_size=300,
    chunk_overlap=100
)

# ✅ Split the Python content into chunks
chunks = python_splitter.split_documents(python_data)

# ✅ Print the first 3 chunks
for i, chunk in enumerate(chunks[:7]):
    print(f"Chunk {i+1}:\n{chunk.page_content}\n")



from langchain_text_splitters import TokenTextSplitter
import tiktoken

# Get the encoding for gpt-4o-mini
encoding = tiktoken.encoding_for_model('gpt-4o-mini')

# Create a token text splitter
token_splitter = TokenTextSplitter(encoding_name=encoding.name, chunk_size=100, chunk_overlap=10)

# Split the PDF into chunks
chunks = token_splitter.split_documents(documents)

for i, chunk in enumerate(chunks[:3]):
    print(f"Chunk {i+1}:\nNo. tokens: {len(encoding.encode(chunk.page_content))}\n{chunk}\n")

from langchain_community.text_splitters import SemanticChunker
from langchain_openai import OpenAIEmbeddings
from euriai_embeddings import EuriAIEmbeddings

# Instantiate an OpenAI embeddings model
embedding_model = EuriAIEmbeddings(api_key=EURI_API_KEY, model='text-embedding-3-small')

# Create the semantic text splitter with desired parameters
semantic_splitter = SemanticChunker(
    embeddings=embedding_model, breakpoint_threshold_type="gradient", breakpoint_threshold_amount=0.8
)

# Split the document
chunks = semantic_splitter.split_documents(documents)
print(chunks[0])

from langchain_community.retrievers import BM25Retriever
from langchain_core.documents import Document # Still needed as invoke returns Document objects

# Changed chunks to be a list of strings, as expected by from_texts
chunks = [
    "RAG stands for Retrieval Augmented Generation.",
    "Graph Retrieval Augmented Generation uses graphs to store and utilize relationships between documents in the retrieval process.",
    "There are different types of RAG architectures; for example, Graph RAG."
]

# Initialize the BM25 retriever using from_texts, configuring it to retrieve three documents at a time.
bm25_retriever = BM25Retriever.from_texts(chunks, k=3)

# Invoke the retriever
results = bm25_retriever.invoke("Graph RAG")

# Extract the page content from the first result
print("Most Relevant Document:")
# Ensure to access the page_content attribute of the Document object
print(results[0].page_content)


# Create a BM25 retriever from chunks
retriever = BM25Retriever.from_documents(
    documents= chunks,
    k=5
)
# Create the LCEL retrieval chain
chain = ({"context": retriever, "question": RunnablePassthrough()}
         | prompt
         | llm
         | StrOutputParser()
)

print(chain.invoke("What are knowledge-intensive tasks?"))

