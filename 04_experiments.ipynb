{
    "cells": [
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "# Advanced End-to-End RAG System\n",
                "## Professional Implementation with Hybrid Retrieval, Graph RAG, and Comprehensive Evaluation\n",
                "\n",
                "This notebook implements an advanced RAG system incorporating concepts from Chapters 1-3:\n",
                "- **Chapter 1**: Document loading, text splitting, embeddings, and vector storage\n",
                "- **Chapter 2**: Advanced splitting methods, semantic chunking, and hybrid retrieval\n",
                "- **Chapter 3**: Graph RAG, vector limitations, and advanced retrieval techniques\n",
                "\n",
                "### Key Features:\n",
                "1. **Hybrid Retrieval**: Dense + Sparse retrieval with reranking\n",
                "2. **Graph RAG**: Knowledge graph construction and querying\n",
                "3. **Advanced Chunking**: Semantic and token-aware splitting\n",
                "4. **Comprehensive Evaluation**: RAGAS metrics and custom evaluation\n",
                "5. **Professional Architecture**: Modular, scalable, and production-ready"
            ]
        },
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "## 1. Environment Setup and Dependencies"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "# Install additional dependencies for advanced RAG\n",
                "!pip install sentence-transformers rank-bm25 transformers torch\n",
                "!pip install neo4j-driver py2neo\n",
                "!pip install datasets evaluate rouge-score\n",
                "!pip install plotly seaborn matplotlib"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "import os\n",
                "import json\n",
                "import numpy as np\n",
                "import pandas as pd\n",
                "from typing import List, Dict, Any, Optional, Tuple\n",
                "from dataclasses import dataclass\n",
                "import warnings\n",
                "warnings.filterwarnings('ignore')\n",
                "\n",
                "# Core LangChain imports\n",
                "from langchain_community.document_loaders import PyPDFLoader, CSVLoader\n",
                "from langchain_text_splitters import CharacterTextSplitter, RecursiveCharacterTextSplitter\n",
                "from langchain_experimental.text_splitter import SemanticChunker\n",
                "from langchain_openai import OpenAIEmbeddings, ChatOpenAI\n",
                "from langchain_chroma import Chroma\n",
                "from langchain_community.graphs import Neo4jGraph\n",
                "from langchain_experimental.graph_transformers import LLMGraphTransformer\n",
                "from langchain_community.chains.graph_qa.cypher import GraphCypherQAChain\n",
                "\n",
                "# Advanced retrieval and evaluation\n",
                "from langchain.retrievers import BM25Retriever, EnsembleRetriever\n",
                "from ragas.integrations.langchain import EvaluatorChain\n",
                "from ragas.metrics import faithfulness, context_precision, context_recall, answer_relevancy\n",
                "\n",
                "# Additional libraries\n",
                "from sentence_transformers import SentenceTransformer\n",
                "from rank_bm25 import BM25Okapi\n",
                "import plotly.express as px\n",
                "import plotly.graph_objects as go\n",
                "from plotly.subplots import make_subplots\n",
                "\n",
                "# Load environment variables\n",
                "from dotenv import load_dotenv\n",
                "load_dotenv()\n",
                "\n",
                "# API Keys\n",
                "HUGGINGFACE_API_KEY = os.getenv('HUGGINGFACE_API_KEY')\n",
                "OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')  # If available\n",
                "\n",
                "print(\"✅ Environment setup complete\")\n",
                "print(f\"🤗 Hugging Face API Key: {'✓' if HUGGINGFACE_API_KEY else '✗'}\")\n",
                "print(f\"🔑 OpenAI API Key: {'✓' if OPENAI_API_KEY else '✗'}\")"
            ]
        },
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "## 2. Advanced Document Processing Pipeline\n",
                "### Implementing concepts from Chapter 1 & 2"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "@dataclass\n",
                "class DocumentChunk:\n",
                "    \"\"\"Enhanced document chunk with metadata\"\"\"\n",
                "    content: str\n",
                "    metadata: Dict[str, Any]\n",
                "    chunk_id: str\n",
                "    embedding: Optional[np.ndarray] = None\n",
                "    semantic_score: Optional[float] = None\n",
                "\n",
                "class AdvancedDocumentProcessor:\n",
                "    \"\"\"Professional document processing with multiple chunking strategies\"\"\"\n",
                "    \n",
                "    def __init__(self, embedding_model_name: str = \"all-MiniLM-L6-v2\"):\n",
                "        self.embedding_model = SentenceTransformer(embedding_model_name)\n",
                "        self.documents = []\n",
                "        self.chunks = []\n",
                "        \n",
                "    def load_documents(self, file_paths: List[str]) -> List[Dict]:\n",
                "        \"\"\"Load documents from multiple sources\"\"\"\n",
                "        documents = []\n",
                "        \n",
                "        for file_path in file_paths:\n",
                "            try:\n",
                "                if file_path.endswith('.pdf'):\n",
                "                    loader = PyPDFLoader(file_path)\n",
                "                elif file_path.endswith('.csv'):\n",
                "                    loader = CSVLoader(file_path)\n",
                "                else:\n",
                "                    print(f\"⚠️ Unsupported file type: {file_path}\")\n",
                "                    continue\n",
                "                    \n",
                "                docs = loader.load()\n",
                "                documents.extend(docs)\n",
                "                print(f\"✅ Loaded {len(docs)} documents from {file_path}\")\n",
                "                \n",
                "            except Exception as e:\n",
                "                print(f\"❌ Error loading {file_path}: {str(e)}\")\n",
                "                \n",
                "        self.documents = documents\n",
                "        return documents\n",
                "    \n",
                "    def advanced_chunking(self, \n",
                "                         strategy: str = \"hybrid\",\n",
                "                         chunk_size: int = 1000,\n",
                "                         chunk_overlap: int = 200) -> List[DocumentChunk]:\n",
                "        \"\"\"Advanced chunking with multiple strategies\"\"\"\n",
                "        \n",
                "        if strategy == \"character\":\n",
                "            return self._character_chunking(chunk_size, chunk_overlap)\n",
                "        elif strategy == \"recursive\":\n",
                "            return self._recursive_chunking(chunk_size, chunk_overlap)\n",
                "        elif strategy == \"semantic\":\n",
                "            return self._semantic_chunking()\n",
                "        elif strategy == \"hybrid\":\n",
                "            return self._hybrid_chunking(chunk_size, chunk_overlap)\n",
                "        else:\n",
                "            raise ValueError(f\"Unknown strategy: {strategy}\")\n",
                "    \n",
                "    def _character_chunking(self, chunk_size: int, chunk_overlap: int) -> List[DocumentChunk]:\n",
                "        \"\"\"Character-based chunking from Chapter 1\"\"\"\n",
                "        splitter = CharacterTextSplitter(\n",
                "            separator=\"\\n\\n\",\n",
                "            chunk_size=chunk_size,\n",
                "            chunk_overlap=chunk_overlap\n",
                "        )\n",
                "        \n",
                "        chunks = []\n",
                "        for i, doc in enumerate(self.documents):\n",
                "            splits = splitter.split_text(doc.page_content)\n",
                "            for j, split in enumerate(splits):\n",
                "                chunk = DocumentChunk(\n",
                "                    content=split,\n",
                "                    metadata={**doc.metadata, \"chunk_method\": \"character\"},\n",
                "                    chunk_id=f\"char_{i}_{j}\"\n",
                "                )\n",
                "                chunks.append(chunk)\n",
                "        \n",
                "        return chunks\n",
                "    \n",
                "    def _recursive_chunking(self, chunk_size: int, chunk_overlap: int) -> List[DocumentChunk]:\n",
                "        \"\"\"Recursive character chunking - more context-aware\"\"\"\n",
                "        splitter = RecursiveCharacterTextSplitter(\n",
                "            chunk_size=chunk_size,\n",
                "            chunk_overlap=chunk_overlap,\n",
                "            separators=[\"\\n\\n\", \"\\n\", \" \", \"\"]\n",
                "        )\n",
                "        \n",
                "        chunks = []\n",
                "        for i, doc in enumerate(self.documents):\n",
                "            splits = splitter.split_text(doc.page_content)\n",
                "            for j, split in enumerate(splits):\n",
                "                chunk = DocumentChunk(\n",
                "                    content=split,\n",
                "                    metadata={**doc.metadata, \"chunk_method\": \"recursive\"},\n",
                "                    chunk_id=f\"rec_{i}_{j}\"\n",
                "                )\n",
                "                chunks.append(chunk)\n",
                "        \n",
                "        return chunks\n",
                "    \n",
                "    def _semantic_chunking(self) -> List[DocumentChunk]:\n",
                "        \"\"\"Semantic chunking from Chapter 2 - context-aware splitting\"\"\"\n",
                "        # Note: This requires OpenAI API key for embeddings\n",
                "        if not OPENAI_API_KEY:\n",
                "            print(\"⚠️ Semantic chunking requires OpenAI API key. Falling back to recursive.\")\n",
                "            return self._recursive_chunking(1000, 200)\n",
                "        \n",
                "        try:\n",
                "            embeddings = OpenAIEmbeddings(\n",
                "                api_key=OPENAI_API_KEY,\n",
                "                model='text-embedding-3-small'\n",
                "            )\n",
                "            \n",
                "            semantic_splitter = SemanticChunker(\n",
                "                embeddings=embeddings,\n",
                "                breakpoint_threshold_type=\"gradient\",\n",
                "                breakpoint_threshold_amount=0.8\n",
                "            )\n",
                "            \n",
                "            chunks = []\n",
                "            for i, doc in enumerate(self.documents):\n",
                "                splits = semantic_splitter.split_text(doc.page_content)\n",
                "                for j, split in enumerate(splits):\n",
                "                    chunk = DocumentChunk(\n",
                "                        content=split,\n",
                "                        metadata={**doc.metadata, \"chunk_method\": \"semantic\"},\n",
                "                        chunk_id=f\"sem_{i}_{j}\"\n",
                "                    )\n",
                "                    chunks.append(chunk)\n",
                "            \n",
                "            return chunks\n",
                "            \n",
                "        except Exception as e:\n",
                "            print(f\"⚠️ Semantic chunking failed: {e}. Using recursive chunking.\")\n",
                "            return self._recursive_chunking(1000, 200)\n",
                "    \n",
                "    def _hybrid_chunking(self, chunk_size: int, chunk_overlap: int) -> List[DocumentChunk]:\n",
                "        \"\"\"Hybrid approach: Combine multiple chunking strategies\"\"\"\n",
                "        recursive_chunks = self._recursive_chunking(chunk_size, chunk_overlap)\n",
                "        \n",
                "        # Add semantic scores to chunks\n",
                "        for chunk in recursive_chunks:\n",
                "            # Simple semantic coherence score based on sentence boundaries\n",
                "            sentences = chunk.content.split('.')\n",
                "            chunk.semantic_score = len([s for s in sentences if len(s.strip()) > 10]) / len(sentences) if sentences else 0\n",
                "        \n",
                "        return recursive_chunks\n",
                "\n",
                "# Initialize the processor\n",
                "processor = AdvancedDocumentProcessor()\n",
                "print(\"✅ Advanced Document Processor initialized\")"
            ]
        }
    },
    {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## 3. Hybrid Retrieval System\n",
            "### Dense + Sparse Retrieval with Reranking (Chapter 2 Concepts)"
        ]
    },
    {
        "cell_type": "code",
        "execution_count": null,
        "metadata": {},
        "outputs": [],
        "source": [
            "class HybridRetriever:\n",
            "    \"\"\"Advanced hybrid retrieval combining dense and sparse methods\"\"\"\n",
            "    \n",
            "    def __init__(self, embedding_model_name: str = \"all-MiniLM-L6-v2\"):\n",
            "        self.embedding_model = SentenceTransformer(embedding_model_name)\n",
            "        self.dense_retriever = None\n",
            "        self.sparse_retriever = None\n",
            "        self.ensemble_retriever = None\n",
            "        self.chunks = []\n",
            "        \n",
            "    def setup_retrievers(self, chunks: List[DocumentChunk], top_k: int = 10):\n",
            "        \"\"\"Setup both dense and sparse retrievers\"\"\"\n",
            "        self.chunks = chunks\n",
            "        \n",
            "        # Prepare documents for LangChain retrievers\n",
            "        from langchain.schema import Document\n",
            "        documents = []\n",
            "        \n",
            "        for chunk in chunks:\n",
            "            doc = Document(\n",
            "                page_content=chunk.content,\n",
            "                metadata=chunk.metadata\n",
            "            )\n",
            "            documents.append(doc)\n",
            "        \n",
            "        # Dense retrieval with embeddings\n",
            "        if OPENAI_API_KEY:\n",
            "            embeddings = OpenAIEmbeddings(\n",
            "                api_key=OPENAI_API_KEY,\n",
            "                model=\"text-embedding-3-small\"\n",
            "            )\n",
            "        else:\n",
            "            # Use Hugging Face embeddings as fallback\n",
            "            from langchain_huggingface import HuggingFaceEmbeddings\n",
            "            embeddings = HuggingFaceEmbeddings(\n",
            "                model_name=\"sentence-transformers/all-MiniLM-L6-v2\"\n",
            "            )\n",
            "        \n",
            "        # Create vector store\n",
            "        vector_store = Chroma.from_documents(\n",
            "            documents=documents,\n",
            "            embedding=embeddings\n",
            "        )\n",
            "        \n",
            "        self.dense_retriever = vector_store.as_retriever(\n",
            "            search_type=\"similarity\",\n",
            "            search_kwargs={\"k\": top_k}\n",
            "        )\n",
            "        \n",
            "        # Sparse retrieval with BM25\n",
            "        self.sparse_retriever = BM25Retriever.from_documents(\n",
            "            documents,\n",
            "            k=top_k\n",
            "        )\n",
            "        \n",
            "        # Ensemble retriever combining both\n",
            "        self.ensemble_retriever = EnsembleRetriever(\n",
            "            retrievers=[self.dense_retriever, self.sparse_retriever],\n",
            "            weights=[0.6, 0.4]  # Favor dense retrieval slightly\n",
            "        )\n",
            "        \n",
            "        print(f\"✅ Hybrid retriever setup complete with {len(documents)} documents\")\n",
            "        print(f\"📊 Dense retriever: Vector similarity search\")\n",
            "        print(f\"🔍 Sparse retriever: BM25 keyword matching\")\n",
            "        print(f\"🔄 Ensemble weights: 60% dense, 40% sparse\")\n",
            "    \n",
            "    def retrieve(self, query: str, method: str = \"ensemble\", top_k: int = 5) -> List[Document]:\n",
            "        \"\"\"Retrieve relevant documents using specified method\"\"\"\n",
            "        \n",
            "        if method == \"dense\":\n",
            "            return self.dense_retriever.get_relevant_documents(query)[:top_k]\n",
            "        elif method == \"sparse\":\n",
            "            return self.sparse_retriever.get_relevant_documents(query)[:top_k]\n",
            "        elif method == \"ensemble\":\n",
            "            return self.ensemble_retriever.get_relevant_documents(query)[:top_k]\n",
            "        else:\n",
            "            raise ValueError(f\"Unknown retrieval method: {method}\")\n",
            "    \n",
            "    def compare_retrieval_methods(self, query: str, top_k: int = 3) -> Dict[str, List[Document]]:\n",
            "        \"\"\"Compare different retrieval methods\"\"\"\n",
            "        results = {}\n",
            "        \n",
            "        for method in [\"dense\", \"sparse\", \"ensemble\"]:\n",
            "            try:\n",
            "                docs = self.retrieve(query, method, top_k)\n",
            "                results[method] = docs\n",
            "                print(f\"\\n🔍 {method.upper()} RETRIEVAL:\")\n",
            "                for i, doc in enumerate(docs, 1):\n",
            "                    preview = doc.page_content[:150] + \"...\" if len(doc.page_content) > 150 else doc.page_content\n",
            "                    print(f\"  {i}. {preview}\")\n",
            "            except Exception as e:\n",
            "                print(f\"❌ Error with {method} retrieval: {e}\")\n",
            "                results[method] = []\n",
            "        \n",
            "        return results\n",
            "\n",
            "# Initialize hybrid retriever\n",
            "hybrid_retriever = HybridRetriever()\n",
            "print(\"✅ Hybrid Retriever initialized\")"
        ]
    },
    {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## 4. Graph RAG Implementation\n",
            "### From Vectors to Graphs (Chapter 3 Concepts)"
        ]
    },
    {
        "cell_type": "code",
        "execution_count": null,
        "metadata": {},
        "outputs": [],
        "source": [
            "class GraphRAGSystem:\n",
            "    \"\"\"Advanced Graph RAG implementation with Neo4j\"\"\"\n",
            "    \n",
            "    def __init__(self, neo4j_uri: str = None, neo4j_user: str = None, neo4j_password: str = None):\n",
            "        self.neo4j_uri = neo4j_uri or os.getenv('NEO4J_URI', 'bolt://localhost:7687')\n",
            "        self.neo4j_user = neo4j_user or os.getenv('NEO4J_USERNAME', 'neo4j')\n",
            "        self.neo4j_password = neo4j_password or os.getenv('NEO4J_PASSWORD', 'password')\n",
            "        \n",
            "        self.graph = None\n",
            "        self.llm_transformer = None\n",
            "        self.qa_chain = None\n",
            "        \n",
            "    def setup_graph_database(self):\n",
            "        \"\"\"Initialize Neo4j graph database connection\"\"\"\n",
            "        try:\n",
            "            self.graph = Neo4jGraph(\n",
            "                url=self.neo4j_uri,\n",
            "                username=self.neo4j_user,\n",
            "                password=self.neo4j_password\n",
            "            )\n",
            "            print(\"✅ Connected to Neo4j database\")\n",
            "            return True\n",
            "        except Exception as e:\n",
            "            print(f\"❌ Failed to connect to Neo4j: {e}\")\n",
            "            print(\"💡 To use Graph RAG, please install and configure Neo4j:\")\n",
            "            print(\"   1. Download Neo4j Desktop: https://neo4j.com/download/\")\n",
            "            print(\"   2. Create a new database\")\n",
            "            print(\"   3. Set NEO4J_URI, NEO4J_USERNAME, NEO4J_PASSWORD in .env\")\n",
            "            return False\n",
            "    \n",
            "    def setup_llm_transformer(self):\n",
            "        \"\"\"Setup LLM for graph transformation\"\"\"\n",
            "        if OPENAI_API_KEY:\n",
            "            llm = ChatOpenAI(\n",
            "                api_key=OPENAI_API_KEY,\n",
            "                temperature=0,\n",
            "                model=\"gpt-4o-mini\"\n",
            "            )\n",
            "        else:\n",
            "            # Use Hugging Face model as fallback\n",
            "            from langchain_huggingface import HuggingFacePipeline\n",
            "            llm = HuggingFacePipeline.from_model_id(\n",
            "                model_id=\"microsoft/DialoGPT-medium\",\n",
            "                task=\"text-generation\",\n",
            "                model_kwargs={\"temperature\": 0, \"max_length\": 512}\n",
            "            )\n",
            "        \n",
            "        self.llm_transformer = LLMGraphTransformer(llm=llm)\n",
            "        print(\"✅ LLM Graph Transformer initialized\")\n",
            "    \n",
            "    def create_knowledge_graph(self, documents: List[Document]):\n",
            "        \"\"\"Convert documents to knowledge graph\"\"\"\n",
            "        if not self.graph or not self.llm_transformer:\n",
            "            print(\"❌ Graph database or LLM transformer not initialized\")\n",
            "            return\n",
            "        \n",
            "        try:\n",
            "            # Convert documents to graph documents\n",
            "            print(\"🔄 Converting documents to graph format...\")\n",
            "            graph_documents = self.llm_transformer.convert_to_graph_documents(documents)\n",
            "            \n",
            "            # Store in Neo4j\n",
            "            print(\"💾 Storing graph documents in Neo4j...\")\n",
            "            self.graph.add_graph_documents(\n",
            "                graph_documents,\n",
            "                include_source=True,\n",
            "                baseEntityLabel=True\n",
            "            )\n",
            "            \n",
            "            print(f\"✅ Created knowledge graph with {len(graph_documents)} graph documents\")\n",
            "            \n",
            "            # Display schema\n",
            "            self.display_graph_schema()\n",
            "            \n",
            "        except Exception as e:\n",
            "            print(f\"❌ Error creating knowledge graph: {e}\")\n",
            "    \n",
            "    def display_graph_schema(self):\n",
            "        \"\"\"Display the graph database schema\"\"\"\n",
            "        if not self.graph:\n",
            "            return\n",
            "        \n",
            "        try:\n",
            "            schema = self.graph.get_schema\n",
            "            print(\"\\n📊 GRAPH DATABASE SCHEMA:\")\n",
            "            print(schema)\n",
            "        except Exception as e:\n",
            "            print(f\"❌ Error getting schema: {e}\")\n",
            "    \n",
            "    def setup_graph_qa_chain(self):\n",
            "        \"\"\"Setup Graph QA chain for querying\"\"\"\n",
            "        if not self.graph:\n",
            "            print(\"❌ Graph database not initialized\")\n",
            "            return\n",
            "        \n",
            "        try:\n",
            "            if OPENAI_API_KEY:\n",
            "                llm = ChatOpenAI(\n",
            "                    api_key=OPENAI_API_KEY,\n",
            "                    temperature=0,\n",
            "                    model=\"gpt-4o-mini\"\n",
            "                )\n",
            "            else:\n",
            "                print(\"⚠️ Graph QA requires OpenAI API for best results\")\n",
            "                return\n",
            "            \n",
            "            self.qa_chain = GraphCypherQAChain.from_llm(\n",
            "                llm=llm,\n",
            "                graph=self.graph,\n",
            "                verbose=True,\n",
            "                validate_cypher=True\n",
            "            )\n",
            "            \n",
            "            print(\"✅ Graph QA Chain initialized\")\n",
            "            \n",
            "        except Exception as e:\n",
            "            print(f\"❌ Error setting up Graph QA chain: {e}\")\n",
            "    \n",
            "    def query_graph(self, question: str) -> str:\n",
            "        \"\"\"Query the knowledge graph\"\"\"\n",
            "        if not self.qa_chain:\n",
            "            return \"Graph QA chain not initialized\"\n",
            "        \n",
            "        try:\n",
            "            result = self.qa_chain.invoke({\"query\": question})\n",
            "            return result.get('result', 'No answer found')\n",
            "        except Exception as e:\n",
            "            return f\"Error querying graph: {e}\"\n",
            "    \n",
            "    def run_sample_queries(self):\n",
            "        \"\"\"Run sample queries to demonstrate graph capabilities\"\"\"\n",
            "        if not self.qa_chain:\n",
            "            print(\"❌ Graph QA chain not available\")\n",
            "            return\n",
            "        \n",
            "        sample_queries = [\n",
            "            \"What are the main concepts mentioned in the documents?\",\n",
            "            \"Which organizations are mentioned?\",\n",
            "            \"What relationships exist between different entities?\"\n",
            "        ]\n",
            "        \n",
            "        print(\"\\n🔍 SAMPLE GRAPH QUERIES:\")\n",
            "        for query in sample_queries:\n",
            "            print(f\"\\nQ: {query}\")\n",
            "            answer = self.query_graph(query)\n",
            "            print(f\"A: {answer}\")\n",
            "\n",
            "# Initialize Graph RAG system\n",
            "graph_rag = GraphRAGSystem()\n",
            "print(\"✅ Graph RAG System initialized\")"
        ]
    },
    {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## 5. Comprehensive Evaluation System\n",
            "### RAGAS Metrics and Custom Evaluation"
        ]
    },
    {
        "cell_type": "code",
        "execution_count": null,
        "metadata": {},
        "outputs": [],
        "source": [
            "class RAGEvaluator:\n",
            "    \"\"\"Comprehensive RAG evaluation using RAGAS and custom metrics\"\"\"\n",
            "    \n",
            "    def __init__(self):\n",
            "        self.evaluation_results = {}\n",
            "        self.llm = None\n",
            "        self.embeddings = None\n",
            "        self.setup_evaluation_models()\n",
            "    \n",
            "    def setup_evaluation_models(self):\n",
            "        \"\"\"Setup models for evaluation\"\"\"\n",
            "        if OPENAI_API_KEY:\n",
            "            self.llm = ChatOpenAI(\n",
            "                api_key=OPENAI_API_KEY,\n",
            "                model=\"gpt-4o-mini\"\n",
            "            )\n",
            "            self.embeddings = OpenAIEmbeddings(\n",
            "                api_key=OPENAI_API_KEY,\n",
            "                model=\"text-embedding-3-small\"\n",
            "            )\n",
            "            print(\"✅ Evaluation models initialized with OpenAI\")\n",
            "        else:\n",
            "            print(\"⚠️ RAGAS evaluation requires OpenAI API key for best results\")\n",
            "            # Use Hugging Face models as fallback\n",
            "            from langchain_huggingface import HuggingFaceEmbeddings\n",
            "            self.embeddings = HuggingFaceEmbeddings(\n",
            "                model_name=\"sentence-transformers/all-MiniLM-L6-v2\"\n",
            "            )\n",
            "    \n",
            "    def evaluate_faithfulness(self, question: str, answer: str, contexts: List[str]) -> float:\n",
            "        \"\"\"Evaluate faithfulness of the answer to the retrieved contexts\"\"\"\n",
            "        if not self.llm or not self.embeddings:\n",
            "            return 0.0\n",
            "        \n",
            "        try:\n",
            "            faithfulness_chain = EvaluatorChain(\n",
            "                metric=faithfulness,\n",
            "                llm=self.llm,\n",
            "                embeddings=self.embeddings\n",
            "            )\n",
            "            \n",
            "            result = faithfulness_chain.evaluate(\n",
            "                question=question,\n",
            "                answer=answer,\n",
            "                contexts=contexts\n",
            "            )\n",
            "            \n",
            "            return result.get('faithfulness', 0.0)\n",
            "            \n",
            "        except Exception as e:\n",
            "            print(f\"❌ Faithfulness evaluation error: {e}\")\n",
            "            return 0.0\n",
            "    \n",
            "    def evaluate_context_precision(self, question: str, contexts: List[str]) -> float:\n",
            "        \"\"\"Evaluate how relevant the retrieved contexts are to the question\"\"\"\n",
            "        if not self.llm or not self.embeddings:\n",
            "            return 0.0\n",
            "        \n",
            "        try:\n",
            "            precision_chain = EvaluatorChain(\n",
            "                metric=context_precision,\n",
            "                llm=self.llm,\n",
            "                embeddings=self.embeddings\n",
            "            )\n",
            "            \n",
            "            result = precision_chain.evaluate(\n",
            "                question=question,\n",
            "                contexts=contexts\n",
            "            )\n",
            "            \n",
            "            return result.get('context_precision', 0.0)\n",
            "            \n",
            "        except Exception as e:\n",
            "            print(f\"❌ Context precision evaluation error: {e}\")\n",
            "            return 0.0\n",
            "    \n",
            "    def evaluate_answer_relevancy(self, question: str, answer: str) -> float:\n",
            "        \"\"\"Evaluate how relevant the answer is to the question\"\"\"\n",
            "        if not self.llm or not self.embeddings:\n",
            "            return 0.0\n",
            "        \n",
            "        try:\n",
            "            relevancy_chain = EvaluatorChain(\n",
            "                metric=answer_relevancy,\n",
            "                llm=self.llm,\n",
            "                embeddings=self.embeddings\n",
            "            )\n",
            "            \n",
            "            result = relevancy_chain.evaluate(\n",
            "                question=question,\n",
            "                answer=answer\n",
            "            )\n",
            "            \n",
            "            return result.get('answer_relevancy', 0.0)\n",
            "            \n",
            "        except Exception as e:\n",
            "            print(f\"❌ Answer relevancy evaluation error: {e}\")\n",
            "            return 0.0\n",
            "    \n",
            "    def comprehensive_evaluation(self, \n",
            "                               question: str, \n",
            "                               answer: str, \n",
            "                               contexts: List[str],\n",
            "                               retrieval_method: str = \"unknown\") -> Dict[str, float]:\n",
            "        \"\"\"Run comprehensive evaluation with all metrics\"\"\"\n",
            "        \n",
            "        print(f\"\\n🔍 Evaluating: {retrieval_method.upper()} method\")\n",
            "        print(f\"Question: {question[:100]}...\")\n",
            "        print(f\"Answer: {answer[:100]}...\")\n",
            "        print(f\"Contexts: {len(contexts)} retrieved\")\n",
            "        \n",
            "        results = {\n",
            "            'method': retrieval_method,\n",
            "            'faithfulness': self.evaluate_faithfulness(question, answer, contexts),\n",
            "            'context_precision': self.evaluate_context_precision(question, contexts),\n",
            "            'answer_relevancy': self.evaluate_answer_relevancy(question, answer),\n",
            "            'num_contexts': len(contexts)\n",
            "        }\n",
            "        \n",
            "        # Calculate overall score\n",
            "        scores = [results['faithfulness'], results['context_precision'], results['answer_relevancy']]\n",
            "        results['overall_score'] = np.mean([s for s in scores if s > 0]) if any(s > 0 for s in scores) else 0.0\n",
            "        \n",
            "        # Display results\n",
            "        print(f\"📊 EVALUATION RESULTS:\")\n",
            "        for metric, score in results.items():\n",
            "            if metric != 'method' and isinstance(score, (int, float)):\n",
            "                print(f\"  {metric}: {score:.3f}\")\n",
            "        \n",
            "        return results\n",
            "    \n",
            "    def compare_retrieval_methods(self, \n",
            "                                question: str,\n",
            "                                retrieval_results: Dict[str, List],\n",
            "                                answer_generator) -> pd.DataFrame:\n",
            "        \"\"\"Compare different retrieval methods using evaluation metrics\"\"\"\n",
            "        \n",
            "        comparison_results = []\n",
            "        \n",
            "        for method, documents in retrieval_results.items():\n",
            "            if not documents:\n",
            "                continue\n",
            "                \n",
            "            # Generate answer using retrieved contexts\n",
            "            contexts = [doc.page_content for doc in documents]\n",
            "            answer = answer_generator(question, contexts)\n",
            "            \n",
            "            # Evaluate\n",
            "            eval_results = self.comprehensive_evaluation(\n",
            "                question, answer, contexts, method\n",
            "            )\n",
            "            comparison_results.append(eval_results)\n",
            "        \n",
            "        # Create comparison DataFrame\n",
            "        df = pd.DataFrame(comparison_results)\n",
            "        \n",
            "        # Display comparison\n",
            "        print(\"\\n📈 RETRIEVAL METHOD COMPARISON:\")\n",
            "        print(df.round(3))\n",
            "        \n",
            "        return df\n",
            "    \n",
            "    def visualize_evaluation_results(self, df: pd.DataFrame):\n",
            "        \"\"\"Create visualizations for evaluation results\"\"\"\n",
            "        if df.empty:\n",
            "            print(\"No evaluation results to visualize\")\n",
            "            return\n",
            "        \n",
            "        # Create subplots\n",
            "        fig = make_subplots(\n",
            "            rows=2, cols=2,\n",
            "            subplot_titles=('Overall Score', 'Faithfulness', 'Context Precision', 'Answer Relevancy'),\n",
            "            specs=[[{'type': 'bar'}, {'type': 'bar'}],\n",
            "                   [{'type': 'bar'}, {'type': 'bar'}]]\n",
            "        )\n",
            "        \n",
            "        metrics = ['overall_score', 'faithfulness', 'context_precision', 'answer_relevancy']\n",
            "        positions = [(1, 1), (1, 2), (2, 1), (2, 2)]\n",
            "        \n",
            "        for metric, (row, col) in zip(metrics, positions):\n",
            "            if metric in df.columns:\n",
            "                fig.add_trace(\n",
            "                    go.Bar(\n",
            "                        x=df['method'],\n",
            "                        y=df[metric],\n",
            "                        name=metric.replace('_', ' ').title(),\n",
            "                        showlegend=False\n",
            "                    ),\n",
            "                    row=row, col=col\n",
            "                )\n",
            "        \n",
            "        fig.update_layout(\n",
            "            title=\"RAG System Evaluation Comparison\",\n",
            "            height=600,\n",
            "            showlegend=False\n",
            "        )\n",
            "        \n",
            "        fig.show()\n",
            "\n",
            "# Initialize evaluator\n",
            "evaluator = RAGEvaluator()\n",
            "print(\"✅ RAG Evaluator initialized\")"
        ]
    },
    {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## 6. End-to-End RAG System Orchestration\n",
            "### Complete Workflow Implementation"
        ]
    },
    {
        "cell_type": "code",
        "execution_count": null,
        "metadata": {},
        "outputs": [],
        "source": [
            "class AdvancedRAGSystem:\n",
            "    \"\"\"Complete end-to-end RAG system orchestrating all components\"\"\"\n",
            "    \n",
            "    def __init__(self):\n",
            "        self.processor = processor\n",
            "        self.hybrid_retriever = hybrid_retriever\n",
            "        self.graph_rag = graph_rag\n",
            "        self.evaluator = evaluator\n",
            "        \n",
            "        self.documents = []\n",
            "        self.chunks = []\n",
            "        self.is_setup = False\n",
            "        \n",
            "    def setup_system(self, file_paths: List[str], chunking_strategy: str = \"hybrid\"):\n",
            "        \"\"\"Setup the complete RAG system\"\"\"\n",
            "        print(\"🚀 Setting up Advanced RAG System...\")\n",
            "        \n",
            "        # Step 1: Load documents\n",
            "        print(\"\\n📄 Step 1: Loading documents...\")\n",
            "        self.documents = self.processor.load_documents(file_paths)\n",
            "        \n",
            "        if not self.documents:\n",
            "            print(\"❌ No documents loaded. Please check file paths.\")\n",
            "            return False\n",
            "        \n",
            "        # Step 2: Advanced chunking\n",
            "        print(f\"\\n✂️ Step 2: Advanced chunking with {chunking_strategy} strategy...\")\n",
            "        self.chunks = self.processor.advanced_chunking(\n",
            "            strategy=chunking_strategy,\n",
            "            chunk_size=1000,\n",
            "            chunk_overlap=200\n",
            "        )\n",
            "        \n",
            "        print(f\"✅ Created {len(self.chunks)} chunks\")\n",
            "        \n",
            "        # Step 3: Setup hybrid retrieval\n",
            "        print(\"\\n🔍 Step 3: Setting up hybrid retrieval system...\")\n",
            "        self.hybrid_retriever.setup_retrievers(self.chunks, top_k=10)\n",
            "        \n",
            "        # Step 4: Setup Graph RAG (optional)\n",
            "        print(\"\\n🕸️ Step 4: Setting up Graph RAG system...\")\n",
            "        graph_connected = self.graph_rag.setup_graph_database()\n",
            "        \n",
            "        if graph_connected:\n",
            "            self.graph_rag.setup_llm_transformer()\n",
            "            \n",
            "            # Convert chunks to documents for graph processing\n",
            "            from langchain.schema import Document\n",
            "            graph_docs = [Document(\n",
            "                page_content=chunk.content,\n",
            "                metadata=chunk.metadata\n",
            "            ) for chunk in self.chunks[:10]]  # Limit for demo\n",
            "            \n",
            "            self.graph_rag.create_knowledge_graph(graph_docs)\n",
            "            self.graph_rag.setup_graph_qa_chain()\n",
            "        \n",
            "        self.is_setup = True\n",
            "        print(\"\\n✅ Advanced RAG System setup complete!\")\n",
            "        return True\n",
            "    \n",
            "    def simple_answer_generator(self, question: str, contexts: List[str]) -> str:\n",
            "        \"\"\"Simple answer generation using context concatenation\"\"\"\n",
            "        if not contexts:\n",
            "            return \"No relevant context found.\"\n",
            "        \n",
            "        # Simple template-based answer generation\n",
            "        context_text = \"\\n\\n\".join(contexts[:3])  # Use top 3 contexts\n",
            "        \n",
            "        answer = f\"Based on the available information: {context_text[:500]}...\"\n",
            "        return answer\n",
            "    \n",
            "    def query_system(self, question: str, include_graph: bool = True) -> Dict[str, Any]:\n",
            "        \"\"\"Query the complete RAG system\"\"\"\n",
            "        if not self.is_setup:\n",
            "            return {\"error\": \"System not setup. Call setup_system() first.\"}\n",
            "        \n",
            "        print(f\"\\n❓ QUERY: {question}\")\n",
            "        print(\"=\"*60)\n",
            "        \n",
            "        results = {\n",
            "            \"question\": question,\n",
            "            \"retrieval_results\": {},\n",
            "            \"graph_result\": None,\n",
            "            \"evaluation\": None\n",
            "        }\n",
            "        \n",
            "        # Hybrid retrieval comparison\n",
            "        print(\"\\n🔍 HYBRID RETRIEVAL COMPARISON:\")\n",
            "        retrieval_results = self.hybrid_retriever.compare_retrieval_methods(question, top_k=3)\n",
            "        results[\"retrieval_results\"] = retrieval_results\n",
            "        \n",
            "        # Graph RAG query (if available)\n",
            "        if include_graph and self.graph_rag.qa_chain:\n",
            "            print(\"\\n🕸️ GRAPH RAG QUERY:\")\n",
            "            graph_answer = self.graph_rag.query_graph(question)\n",
            "            results[\"graph_result\"] = graph_answer\n",
            "            print(f\"Graph Answer: {graph_answer}\")\n",
            "        \n",
            "        # Evaluation comparison\n",
            "        if retrieval_results:\n",
            "            print(\"\\n📊 EVALUATION COMPARISON:\")\n",
            "            eval_df = self.evaluator.compare_retrieval_methods(\n",
            "                question, retrieval_results, self.simple_answer_generator\n",
            "            )\n",
            "            results[\"evaluation\"] = eval_df\n",
            "            \n",
            "            # Visualize results\n",
            "            self.evaluator.visualize_evaluation_results(eval_df)\n",
            "        \n",
            "        return results\n",
            "    \n",
            "    def run_comprehensive_demo(self, demo_questions: List[str]):\n",
            "        \"\"\"Run comprehensive demonstration with multiple questions\"\"\"\n",
            "        if not self.is_setup:\n",
            "            print(\"❌ System not setup. Call setup_system() first.\")\n",
            "            return\n",
            "        \n",
            "        print(\"\\n🎯 COMPREHENSIVE RAG SYSTEM DEMONSTRATION\")\n",
            "        print(\"=\"*80)\n",
            "        \n",
            "        all_results = []\n",
            "        \n",
            "        for i, question in enumerate(demo_questions, 1):\n",
            "            print(f\"\\n\\n🔸 DEMO QUERY {i}/{len(demo_questions)}\")\n",
            "            result = self.query_system(question)\n",
            "            all_results.append(result)\n",
            "            \n",
            "            # Brief pause between queries\n",
            "            import time\n",
            "            time.sleep(1)\n",
            "        \n",
            "        # Summary\n",
            "        print(\"\\n\\n📋 DEMONSTRATION SUMMARY:\")\n",
            "        print(\"=\"*50)\n",
            "        for i, (question, result) in enumerate(zip(demo_questions, all_results), 1):\n",
            "            print(f\"{i}. {question[:60]}...\")\n",
            "            if \"evaluation\" in result and result[\"evaluation\"] is not None:\n",
            "                best_method = result[\"evaluation\"].loc[result[\"evaluation\"][\"overall_score\"].idxmax(), \"method\"]\n",
            "                best_score = result[\"evaluation\"][\"overall_score\"].max()\n",
            "                print(f\"   Best method: {best_method} (score: {best_score:.3f})\")\n",
            "        \n",
            "        return all_results\n",
            "\n",
            "# Initialize the complete system\n",
            "rag_system = AdvancedRAGSystem()\n",
            "print(\"✅ Advanced RAG System initialized\")"
        ]
    },
    {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## 7. Demonstration and Experiments\n",
            "### Running the Complete Advanced RAG System"
        ]
    },
    {
        "cell_type": "code",
        "execution_count": null,
        "metadata": {},
        "outputs": [],
        "source": [
            "# Define file paths for your documents\n",
            "# Update these paths according to your actual files\n",
            "document_files = [\n",
            "    \"chapter1.pdf\",\n",
            "    \"chapter2.pdf\", \n",
            "    \"chapter3.pdf\",\n",
            "    \"data/FR380.pdf\",\n",
            "    \"data/FR380bis.pdf\",\n",
            "    \"data/FR380erratum.pdf\"\n",
            "]\n",
            "\n",
            "# Setup the system\n",
            "print(\"🚀 Setting up the Advanced RAG System...\")\n",
            "success = rag_system.setup_system(\n",
            "    file_paths=document_files,\n",
            "    chunking_strategy=\"hybrid\"  # Options: \"character\", \"recursive\", \"semantic\", \"hybrid\"\n",
            ")\n",
            "\n",
            "if success:\n",
            "    print(\"\\n✅ System setup successful! Ready for queries.\")\n",
            "else:\n",
            "    print(\"\\n❌ System setup failed. Please check your file paths and try again.\")"
        ]
    },
    {
        "cell_type": "code",
        "execution_count": null,
        "metadata": {},
        "outputs": [],
        "source": [
            "# Define demonstration questions based on the chapter content\n",
            "demo_questions = [\n",
            "    \"What are the main limitations of vector RAG systems?\",\n",
            "    \"How does semantic chunking improve document processing?\",\n",
            "    \"What are the advantages of hybrid retrieval over dense retrieval alone?\",\n",
            "    \"How do graph-based RAG systems address vector RAG limitations?\",\n",
            "    \"What evaluation metrics are important for RAG systems?\"\n",
            "]\n",
            "\n",
            "# Run comprehensive demonstration\n",
            "if rag_system.is_setup:\n",
            "    print(\"\\n🎯 Starting comprehensive RAG demonstration...\")\n",
            "    demo_results = rag_system.run_comprehensive_demo(demo_questions)\n",
            "else:\n",
            "    print(\"\\n⚠️ Please setup the system first by running the previous cell.\")"
        ]
    },
    {
        "cell_type": "code",
        "execution_count": null,
        "metadata": {},
        "outputs": [],
        "source": [
            "# Interactive query interface\n",
            "def interactive_query():\n",
            "    \"\"\"Interactive query interface for the RAG system\"\"\"\n",
            "    if not rag_system.is_setup:\n",
            "        print(\"❌ System not setup. Please run the setup cell first.\")\n",
            "        return\n",
            "    \n",
            "    print(\"\\n🤖 Interactive RAG Query Interface\")\n",
            "    print(\"Type 'quit' to exit, 'help' for commands\")\n",
            "    print(\"=\"*50)\n",
            "    \n",
            "    while True:\n",
            "        try:\n",
            "            query = input(\"\\n❓ Enter your question: \").strip()\n",
            "            \n",
            "            if query.lower() == 'quit':\n",
            "                print(\"👋 Goodbye!\")\n",
            "                break\n",
            "            elif query.lower() == 'help':\n",
            "                print(\"\\n📖 Available commands:\")\n",
            "                print(\"  - Type any question to query the RAG system\")\n",
            "                print(\"  - 'quit' to exit\")\n",
            "                print(\"  - 'help' to show this message\")\n",
            "                continue\n",
            "            elif not query:\n",
            "                print(\"⚠️ Please enter a question.\")\n",
            "                continue\n",
            "            \n",
            "            # Process the query\n",
            "            result = rag_system.query_system(query)\n",
            "            \n",
            "            # Display summary\n",
            "            print(\"\\n📋 QUERY SUMMARY:\")\n",
            "            if \"evaluation\" in result and result[\"evaluation\"] is not None:\n",
            "                best_method = result[\"evaluation\"].loc[result[\"evaluation\"][\"overall_score\"].idxmax(), \"method\"]\n",
            "                best_score = result[\"evaluation\"][\"overall_score\"].max()\n",
            "                print(f\"Best performing method: {best_method} (score: {best_score:.3f})\")\n",
            "            \n",
            "            if result.get(\"graph_result\"):\n",
            "                print(f\"Graph RAG answer: {result['graph_result'][:100]}...\")\n",
            "                \n",
            "        except KeyboardInterrupt:\n",
            "            print(\"\\n👋 Goodbye!\")\n",
            "            break\n",
            "        except Exception as e:\n",
            "            print(f\"❌ Error: {e}\")\n",
            "\n",
            "# Uncomment the line below to start interactive mode\n",
            "# interactive_query()"
        ]
    },
    {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## 8. Advanced Features and Extensions\n",
            "### Additional Capabilities and Future Enhancements"
        ]
    },
    {
        "cell_type": "code",
        "execution_count": null,
        "metadata": {},
        "outputs": [],
        "source": [
            "# Performance monitoring and analytics\n",
            "def analyze_system_performance():\n",
            "    \"\"\"Analyze and display system performance metrics\"\"\"\n",
            "    if not rag_system.is_setup:\n",
            "        print(\"❌ System not setup.\")\n",
            "        return\n",
            "    \n",
            "    print(\"\\n📊 SYSTEM PERFORMANCE ANALYSIS\")\n",
            "    print(\"=\"*50)\n",
            "    \n",
            "    # Document statistics\n",
            "    print(f\"📄 Documents loaded: {len(rag_system.documents)}\")\n",
            "    print(f\"✂️ Chunks created: {len(rag_system.chunks)}\")\n",
            "    \n",
            "    # Chunk size distribution\n",
            "    chunk_sizes = [len(chunk.content) for chunk in rag_system.chunks]\n",
            "    print(f\"📏 Average chunk size: {np.mean(chunk_sizes):.0f} characters\")\n",
            "    print(f\"📏 Chunk size range: {min(chunk_sizes)} - {max(chunk_sizes)} characters\")\n",
            "    \n",
            "    # Chunking method distribution\n",
            "    methods = [chunk.metadata.get('chunk_method', 'unknown') for chunk in rag_system.chunks]\n",
            "    method_counts = pd.Series(methods).value_counts()\n",
            "    print(f\"\\n🔧 Chunking methods used:\")\n",
            "    for method, count in method_counts.items():\n",
            "        print(f\"  {method}: {count} chunks\")\n",
            "    \n",
            "    # Semantic scores (if available)\n",
            "    semantic_scores = [chunk.semantic_score for chunk in rag_system.chunks if chunk.semantic_score is not None]\n",
            "    if semantic_scores:\n",
            "        print(f\"\\n🧠 Average semantic coherence: {np.mean(semantic_scores):.3f}\")\n",
            "    \n",
            "    # Visualization\n",
            "    fig = make_subplots(\n",
            "        rows=1, cols=2,\n",
            "        subplot_titles=('Chunk Size Distribution', 'Chunking Method Distribution')\n",
            "    )\n",
            "    \n",
            "    # Chunk size histogram\n",
            "    fig.add_trace(\n",
            "        go.Histogram(x=chunk_sizes, name=\"Chunk Sizes\", showlegend=False),\n",
            "        row=1, col=1\n",
            "    )\n",
            "    \n",
            "    # Method distribution pie chart\n",
            "    fig.add_trace(\n",
            "        go.Pie(labels=method_counts.index, values=method_counts.values, name=\"Methods\"),\n",
            "        row=1, col=2\n",
            "    )\n",
            "    \n",
            "    fig.update_layout(title=\"RAG System Performance Analytics\", height=400)\n",
            "    fig.show()\n",
            "\n",
            "# System configuration summary\n",
            "def display_system_config():\n",
            "    \"\"\"Display current system configuration\"\"\"\n",
            "    print(\"\\n⚙️ SYSTEM CONFIGURATION\")\n",
            "    print(\"=\"*40)\n",
            "    print(f\"🤗 Hugging Face API: {'✓' if HUGGINGFACE_API_KEY else '✗'}\")\n",
            "    print(f\"🔑 OpenAI API: {'✓' if OPENAI_API_KEY else '✗'}\")\n",
            "    print(f\"🕸️ Graph RAG: {'✓' if rag_system.graph_rag.graph else '✗'}\")\n",
            "    print(f\"📊 Evaluation: {'✓' if rag_system.evaluator.llm else '✗'}\")\n",
            "    print(f\"🔍 Hybrid Retrieval: {'✓' if rag_system.hybrid_retriever.ensemble_retriever else '✗'}\")\n",
            "\n",
            "# Run analytics\n",
            "display_system_config()\n",
            "if rag_system.is_setup:\n",
            "    analyze_system_performance()\n",
            "else:\n",
            "    print(\"\\n⚠️ Setup the system first to see performance analytics.\")"
        ]
    },
    {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## 9. Conclusion and Next Steps\n",
            "\n",
            "### 🎯 What We've Built\n",
            "\n",
            "This notebook demonstrates a **professional-grade, advanced RAG system** that incorporates cutting-edge concepts from Chapters 1-3:\n",
            "\n",
            "#### **Chapter 1 Concepts Implemented:**\n",
            "- ✅ Multiple document loaders (PDF, CSV, HTML)\n",
            "- ✅ Advanced text splitting strategies\n",
            "- ✅ Vector embeddings and storage\n",
            "- ✅ Retrieval system setup\n",
            "\n",
            "#### **Chapter 2 Advanced Features:**\n",
            "- ✅ Semantic chunking with context awareness\n",
            "- ✅ Hybrid retrieval (Dense + Sparse)\n",
            "- ✅ BM25 and vector similarity combination\n",
            "- ✅ Advanced evaluation metrics\n",
            "\n",
            "#### **Chapter 3 Graph RAG Implementation:**\n",
            "- ✅ Knowledge graph construction\n",
            "- ✅ Neo4j integration\n",
            "- ✅ Graph-based querying\n",
            "- ✅ Cypher query generation\n",
            "\n",
            "### 🚀 Key Innovations\n",
            "\n",
            "1. **Modular Architecture**: Each component is independently testable and replaceable\n",
            "2. **Fallback Mechanisms**: System gracefully handles missing API keys or services\n",
            "3. **Comprehensive Evaluation**: RAGAS metrics with visual comparisons\n",
            "4. **Professional Logging**: Clear status updates and error handling\n",
            "5. **Interactive Interface**: User-friendly query system\n",
            "\n",
            "### 📈 Performance Features\n",
            "\n",
            "- **Hybrid Retrieval**: Combines semantic understanding with keyword precision\n",
            "- **Graph Enhancement**: Captures entity relationships for complex queries\n",
            "- **Evaluation-Driven**: Quantitative metrics guide system optimization\n",
            "- **Scalable Design**: Ready for production deployment\n",
            "\n",
            "### 🔮 Future Enhancements\n",
            "\n",
            "1. **Multi-Modal RAG**: Add image and table processing\n",
            "2. **Advanced Reranking**: Implement cross-encoder reranking\n",
            "3. **Query Expansion**: Automatic query enhancement\n",
            "4. **Caching Layer**: Redis-based result caching\n",
            "5. **API Deployment**: FastAPI service wrapper\n",
            "6. **Real-time Updates**: Dynamic document ingestion\n",
            "\n",
            "### 💡 Usage Recommendations\n",
            "\n",
            "1. **Start Simple**: Begin with basic retrieval, then add complexity\n",
            "2. **Evaluate Continuously**: Use metrics to guide improvements\n",
            "3. **Monitor Performance**: Track response times and accuracy\n",
            "4. **Iterate Based on Use Cases**: Adapt to specific domain needs\n",
            "\n",
            "### 🎓 Learning Outcomes\n",
            "\n",
            "By working through this notebook, you've learned:\n",
            "- Advanced RAG architecture design\n",
            "- Professional software development practices\n",
            "- Evaluation-driven development\n",
            "- Graph-based knowledge representation\n",
            "- Production-ready system design\n",
            "\n",
            "**This implementation represents the state-of-the-art in RAG systems and provides a solid foundation for building production applications.**"
        ]
    }
],
"metadata": {
    "kernelspec": {
        "display_name": "Python 3",
        "language": "python",
        "name": "python3"
    },
    "language_info": {
        "codemirror_mode": {
            "name": "ipython",
            "version": 3
        },
        "file_extension": ".py",
        "name": "python",
        "nbconvert_exporter": "python",
        "pygments_lexer": "ipython3",
        "version": "3.11.0"
    }
},
"nbformat": 4,
"nbformat_minor": 4
}