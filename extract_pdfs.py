import json
import pdfplumber


def extract_chapter_content(filename):
    try:
        with pdfplumber.open(filename) as pdf:
            content = {'filename': filename,
                       'total_pages': len(pdf.pages), 'content': []}

            # Extract more pages to get comprehensive content
            for i, page in enumerate(pdf.pages):
                text = page.extract_text()
                if text and len(text.strip()) > 100:
                    # Look for detailed technical content, code examples, and advanced concepts
                    keywords = ['rag', 'retrieval', 'vector', 'graph', 'limitation', 'advanced', 'hybrid',
                                'embedding', 'chunk', 'split', 'loader', 'langchain', 'implementation',
                                'code', 'example', 'technique', 'strategy', 'pipeline', 'architecture']

                    # Include first 15 pages
                    if any(keyword in text.lower() for keyword in keywords) or i < 15:
                        content['content'].append({
                            'page': i+1,
                            'text': text
                        })
            return content
    except Exception as e:
        return {'filename': filename, 'error': str(e)}


# Extract and save detailed content for each chapter

all_chapters_content = {}

for chapter in ['chapter1.pdf', 'chapter2.pdf', 'chapter3.pdf']:
    print(f'=== Extracting {chapter.upper()} ===')
    result = extract_chapter_content(chapter)

    if 'error' in result:
        print('Error:', result['error'])
        all_chapters_content[chapter] = {'error': result['error']}
    else:
        print(
            f'Total pages: {result["total_pages"]}, Extracted pages: {len(result["content"])}')
        all_chapters_content[chapter] = result

        # Print key pages with advanced concepts
        # Show first 5 relevant pages
        for page_content in result['content'][:5]:
            page_num = page_content['page']
            text = page_content['text']
            print(f'\nPage {page_num} (first 500 chars):')
            print(text[:500] + '...' if len(text) > 500 else text)
            print('-' * 60)
    print('=' * 80)

# Save all content to JSON for detailed analysis
with open('chapters_content.json', 'w', encoding='utf-8') as f:
    json.dump(all_chapters_content, f, indent=2, ensure_ascii=False)

print('\nContent saved to chapters_content.json for detailed analysis')
