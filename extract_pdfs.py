import pdfplumber

def extract_chapter_content(filename):
    try:
        with pdfplumber.open(filename) as pdf:
            content = {'filename': filename, 'total_pages': len(pdf.pages), 'content': []}
            
            for i, page in enumerate(pdf.pages[:10]):
                text = page.extract_text()
                if text and len(text.strip()) > 50:
                    # Look for key concepts and detailed content
                    if any(keyword in text.lower() for keyword in ['rag', 'retrieval', 'vector', 'graph', 'limitation', 'advanced', 'hybrid']):
                        content['content'].append({
                            'page': i+1,
                            'text': text[:1200]
                        })
            return content
    except Exception as e:
        return {'filename': filename, 'error': str(e)}

for chapter in ['chapter1.pdf', 'chapter2.pdf', 'chapter3.pdf']:
    print(f'=== {chapter.upper()} ===')
    result = extract_chapter_content(chapter)
    
    if 'error' in result:
        print('Error:', result['error'])
    else:
        print('Total pages:', result['total_pages'])
        for page_content in result['content']:
            page_num = page_content['page']
            print(f'Page {page_num}:')
            print(page_content['text'])
            print('-' * 40)
    print('=' * 60)
