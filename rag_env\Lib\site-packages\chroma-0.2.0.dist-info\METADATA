Metadata-Version: 2.4
Name: Chroma
Version: 0.2.0
Summary: Color handling made simple.
Home-page: https://github.com/seenaburns/Chroma
Author: <PERSON><PERSON>
Author-email: <EMAIL>
License: Copyright (c) 2012, <PERSON><PERSON>
        All rights reserved.
        
        Redistribution and use in source and binary forms, with or without
        modification, are permitted provided that the following conditions are met:
            * Redistributions of source code must retain the above copyright
              notice, this list of conditions and the following disclaimer.
            * Redistributions in binary form must reproduce the above copyright
              notice, this list of conditions and the following disclaimer in the
              documentation and/or other materials provided with the distribution.
            * Neither the name of the <organization> nor the
              names of its contributors may be used to endorse or promote products
              derived from this software without specific prior written permission.
        
        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
        ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
        WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
        DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY
        DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
        (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
        LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
        ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
        (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
        SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
        
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Natural Language :: English
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2.6
Classifier: Programming Language :: Python :: 2.7
License-File: LICENSE.txt
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: license
Dynamic: license-file
Dynamic: summary

Chroma
======

Chroma is a Python module for handling colors with ease.

Manipulating colors can quickily escalate into a tedious and complicated task, particularly when you become concerned with color systems beyond RGB. Chroma is here to provide a simple API to do the heavy lifting, so that you can stay focused on the important parts of your projects.

Before you ask, Chroma is BSD licensed, available on `Github <https://github.com/seenaburns/Chroma>`_ and PyPI.

Features
--------
- Hex (#rrggbb, #rrggbbaa)
- RGB
- HLS
- HSV
- CMY, CMYK
- Alpha
- Color Blending: addative and subtractive mixing

Roadmap
-------
- Coordinates (sRGB, YIQ, CIE, LAB and more)
- Color harmonies: complementary, analogous, triad
- Color difference
- Color dectection in images

Quickstart
==========

Getting started with the power of Chroma is meant to be straightfoward:

::

    import chroma

    # Create a color object
    color = chroma.Color('#00FF00')

    # Handling different color systems
    color.cmy = (0.3, 0.7, 0.8)
    color.rgb    # (0.7, 0.3, 0.2)
    color.hls    # (0.0333, 0.45, 0.5556)

    # Alpha
    color.alpha = 0.5
    color.hsv    # (0.03333, 0.7143, 0.7, 0.5)

    # Color blending
    color + chroma.Color("#FF00FF")
    # #FF4DFF

And there you have it. The `Documenation <https://chroma.readthedocs.org/en/latest/>`_ describes Chroma's functionality and usage in detail.

Installation
------------

Installation is as easy as:

::

    pip install chroma

Or if you're an easy_install-er:

::

    easy_install chroma

Chroma does not yet support Python 3, but, if you're interested, contribute!

Contribute
----------
Chroma is under active development and could use your support. Even bug reports, feature suggestions and feedback can help push Chroma forward in the right direction.

Chroma is hosted on `Github <https://github.com/seenaburns/Chroma>`_ and there are a number of ideas of where to start in the `issues section <https://github.com/seenaburns/Chroma/issues>`_.


History
-------

0.2.0 (2013-02-01)
------------------
(Breaks backwards compatibility)
- CMY, CMYK Support
- Color Blending (Addative and Subtractive Mixing)
- Comparison (eq, ne)
- Remove color coordinate properties (direct modification)
- Major Bug Fixes
  - Hex rounding
  - Force alpha between 0 and 1
- Other
  - Hue (HLS, HSV) in degrees not percent of degrees
  - RGB256 alpha in range 0-1, not 0-255
  - Force Hex to output in uppercase
  - ValueError on bad input
  - Extensive documentation on Read the Docs

0.1.3 (2013-01-01)
------------------
- Direct modification of color coordinates

0.1.2 (2012-12-28)
------------------
- Remove HLS256, HSV256 (critical bug)
- Alpha support
- Add RGB, RGB256, HLS, HSV as formats for Color initialization
- Bug fixes

0.1.1 (2012-12-16)
------------------
- HLS, HSV Support
- API changes (setter methods)
- Bug Fixes
- Update to README

0.1.0 (2012-12-15)
------------------
- Initial Release
- RGB and Hex support
