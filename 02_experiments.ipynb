import os
import logging
from typing import List, Dict, Any
from langchain_community.document_loaders import PyPDFLoader, UnstructuredMarkdownLoader, UnstructuredHTMLLoader
from langchain_text_splitters import SemanticChunker, TokenTextSplitter
from langchain_community.retrievers import BM25Retriever
from langchain_chroma import Chroma
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from langchain_community.graphs import Neo4jGraph
from langchain_experimental.graph_transformers import LLMGraphTransformer
from langchain_community.chains.graph_qa.cypher import GraphCypherQAChain
from langchain_core.prompts import FewShotPromptTemplate, PromptTemplate
from ragas.metrics import faithfulness, context_precision, context_recall, answer_relevancy
from ragas.integrations.langchain import EvaluatorChain
import pandas as pd
from pdf2image import convert_from_path
import pytesseract
import streamlit as st
from redis import Redis
from fastapi import FastAPI
from pydantic import BaseModel
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from rank_bm25 import BM25Okapi
import uvicorn
import asyncio

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Initialize components
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY", "...")
llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
embeddings = OpenAIEmbeddings(model="text-embedding-3-small")
redis_client = Redis(host="localhost", port=6379, db=0)
graph = Neo4jGraph(url="bolt://localhost:7687", username="neo4j", password=os.getenv("NEO4J_PASSWORD", "..."))

def load_data() -> List[Dict[str, Any]]:
    """Load multimodal data from PDFs, Markdown, HTML, and CSVs."""
    try:
        loaders = [
            PyPDFLoader("data/raw/FR380.pdf"),
            UnstructuredMarkdownLoader("data/raw/dhs_methods.md"),
            UnstructuredHTMLLoader("data/raw/dhs_web.html")
        ]
        documents = [doc for loader in loaders for doc in loader.load()]
        csv_data = pd.read_csv("data/raw/kdhs_indicators.csv")
        images = convert_from_path("data/raw/FR380.pdf")
        charts = [pytesseract.image_to_string(img) for img in images]
        logger.info(f"Loaded {len(documents)} documents, {len(csv_data)} CSV rows, {len(charts)} charts")
        return {"documents": documents, "csv": csv_data, "charts": charts}
    except Exception as e:
        logger.error(f"Data loading failed: {str(e)}")
        raise


def split_documents(documents: List) -> List:
    """Split documents using semantic and token-based chunking."""
    semantic_splitter = SemanticChunker(embeddings)
    token_splitter = TokenTextSplitter(chunk_size=1000, chunk_overlap=200)
    chunks = semantic_splitter.split_documents(documents)
    chunks = token_splitter.split_documents(chunks)  # Refine chunks
    logger.info(f"Split into {len(chunks)} chunks")
    return chunks

def index_documents(chunks: List) -> tuple[Chroma, BM25Retriever]:
    """Index documents in ChromaDB for vector search and BM25 for sparse search."""
    vector_store = Chroma.from_documents(chunks, embeddings, persist_directory="data/processed/chroma")
    bm25_retriever = BM25Retriever.from_documents(chunks, k=5)
    logger.info("Indexed documents in ChromaDB and BM25")
    return vector_store, bm25_retriever

def build_knowledge_graph(documents: List) -> None:
    """Construct knowledge graph using LLMGraphTransformer."""
    graph_transformer = LLMGraphTransformer(llm=llm)
    graph_documents = graph_transformer.convert_to_graph_documents(documents)
    graph.add_graph_documents(graph_documents)
    logger.info("Built knowledge graph in Neo4j")
    graph.refresh_schema()
    logger.info(f"Graph schema: {graph.get_schema()}")

# Few-shot prompting for Cypher
examples = [
    {
        "question": "Which counties have high child mortality?",
        "query": "MATCH (c:County)-[:HAS_INDICATOR]->(i:Indicator {name: 'Child Mortality'}) WHERE i.rate > 40 RETURN c.name, i.rate"
    },
    {
        "question": "What is the fertility rate in Nairobi?",
        "query": "MATCH (c:County {name: 'Nairobi'})-[:HAS_INDICATOR]->(i:Indicator {name: 'Fertility Rate'}) RETURN i.rate"
    }
]
example_prompt = PromptTemplate.from_template("User input: {question}\nCypher query: {query}")
cypher_prompt = FewShotPromptTemplate(
    examples=examples,
    example_prompt=example_prompt,
    prefix="You are a Neo4j expert. Given an input question, create a syntactically correct Cypher query.\n\nSchema:\n{schema}\n\nExamples:",
    suffix="User input: {question}\nCypher query:",
    input_variables=["question", "schema"]
)



# Hybrid retrieval
async def hybrid_retrieval(query: str, vector_store: Chroma, bm25_retriever: BM25Retriever) -> Dict[str, Any]:
    """Combine vector, sparse, and graph retrieval with caching."""
    cache_key = f"query:{query}"
    if cached := redis_client.get(cache_key):
        logger.info(f"Cache hit for query: {query}")
        return {"results": cached.decode().split("\n"), "source": "cache"}
    
    # Vector retrieval
    vector_results = vector_store.as_retriever(search_kwargs={"k": 5}).invoke(query)
    vector_texts = [r.page_content for r in vector_results]
    
    # Sparse retrieval
    bm25_results = bm25_retriever.invoke(query)
    bm25_texts = [r.page_content for r in bm25_results]
    
    # Graph retrieval
    cypher_chain = GraphCypherQAChain.from_llm(
        graph=graph, llm=llm, validate_cypher=True, verbose=True, cypher_prompt=cypher_prompt
    )
    graph_result = cypher_chain.invoke({"query": query})["result"]
    
    # Combine and rank (simple weighted ensemble)
    combined = list(set(vector_texts + bm25_texts)) + [graph_result]
    weights = [0.6, 0.3, 0.1]  # Vector, sparse, graph
    ranked_results = sorted(combined, key=lambda x: sum([w * (x in src) for w, src in zip(weights, [vector_texts, bm25_texts, [graph_result]])]), reverse=True)
    
    redis_client.setex(cache_key, 3600, "\n".join(ranked_results))
    logger.info(f"Hybrid retrieval completed for query: {query}")
    return {"results": ranked_results, "source": "computed"}

def process_multimodal(query: str, data: Dict[str, Any], vector_store: Chroma, bm25_retriever: BM25Retriever) -> Dict[str, Any]:
    """Process query with text, CSV, and chart data."""
    loop = asyncio.get_event_loop()
    text_results = loop.run_until_complete(hybrid_retrieval(query, vector_store, bm25_retriever))
    csv_result = data["csv"].query(f"indicator.str.contains('{query.split()[-1]}', case=False)", engine="python").to_dict()
    chart_result = next((c for c in data["charts"] if query.lower() in c.lower()), "No relevant chart found")
    logger.info(f"Multimodal processing completed for query: {query}")
    return {"text": text_results["results"], "csv": csv_result, "chart": chart_result, "source": text_results["source"]}


def evaluate_response(query: str, results: Dict[str, Any]) -> Dict[str, float]:
    """Evaluate response using ragas metrics."""
    faithfulness_chain = EvaluatorChain(metric=faithfulness, llm=llm)
    precision_chain = EvaluatorChain(metric=context_precision, llm=llm)
    recall_chain = EvaluatorChain(metric=context_recall, llm=llm)
    relevancy_chain = EvaluatorChain(metric=answer_relevancy, llm=llm)
    
    eval_input = {"question": query, "answer": results["text"][0] if results["text"] else "", "contexts": results["text"]}
    metrics = {
        "faithfulness": faithfulness_chain(eval_input)["faithfulness"],
        "context_precision": precision_chain(eval_input)["context_precision"],
        "context_recall": recall_chain(eval_input)["context_recall"],
        "answer_relevancy": relevancy_chain(eval_input)["answer_relevancy"]
    }
    logger.info(f"Evaluation metrics for query '{query}': {metrics}")
    return metrics


# Streamlit app
def run_streamlit():
    """Run Streamlit frontend."""
    st.set_page_config(page_title="Multimodal RAG for Public Health", layout="wide")
    st.title("Multimodal RAG for Public Health Policy Analysis")
    
    # Initialize data and indexes
    data = load_data()
    chunks = split_documents(data["documents"])
    vector_store, bm25_retriever = index_documents(chunks)
    build_knowledge_graph(data["documents"])
    
    # Query interface
    query = st.text_input("Enter query (e.g., 'Child mortality in KDHS 2022')")
    if query:
        with st.spinner("Processing query..."):
            results = process_multimodal(query, data, vector_store, bm25_retriever)
            metrics = evaluate_response(query, results)
            
            # Display results
            col1, col2 = st.columns(2)
            with col1:
                st.subheader("Results")
                st.write("**Text Results**:")
                for r in results["text"]:
                    st.write(r)
                st.write("**CSV Data**:")
                st.json(results["csv"])
                st.write("**Chart Description**:")
                st.write(results["chart"])
            with col2:
                st.subheader("Evaluation Metrics")
                st.write(f"Faithfulness: {metrics['faithfulness']:.2f}")
                st.write(f"Context Precision: {metrics['context_precision']:.2f}")
                st.write(f"Context Recall: {metrics['context_recall']:.2f}")
                st.write(f"Answer Relevancy: {metrics['answer_relevancy']:.2f}")
                st.write(f"Source: {results['source']}")
                st.subheader("Knowledge Graph Schema")
                st.json(graph.get_schema())

# FastAPI app
app = FastAPI(title="Multimodal RAG API")

class QueryRequest(BaseModel):
    query: str

@app.post("/query")
async def query_endpoint(request: QueryRequest):
    """FastAPI endpoint for query processing."""
    data = load_data()
    chunks = split_documents(data["documents"])
    vector_store, bm25_retriever = index_documents(chunks)
    build_knowledge_graph(data["documents"])
    results = await process_multimodal(request.query, data, vector_store, bm25_retriever)
    metrics = evaluate_response(request.query, results)
    return {"results": results, "metrics": metrics}

# Main execution
if __name__ == "__main__":
    # Run Streamlit or FastAPI based on environment
    if os.getenv("APP_MODE", "streamlit") == "streamlit":
        run_streamlit()
    else:
        uvicorn.run(app, host="0.0.0.0", port=8000)