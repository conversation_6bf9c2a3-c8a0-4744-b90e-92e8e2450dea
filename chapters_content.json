{"chapter1.pdf": {"filename": "chapter1.pdf", "total_pages": 50, "content": [{"page": 1, "text": "Loading Documents\nfor RAG with\nLangChain\nR E T R I E VA L AU G M E N T E D G E N E R AT I O N ( R AG ) W I T H L A N G C H A I N\nMeri Nova\nMachine Learning Engineer"}, {"page": 2, "text": "Meet your instructor...\n<PERSON><PERSON>\nFounder at Break Into Data\nMachine Learning Engineer\nContent Creator on Linkedin and YouTube\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 3, "text": "Retrieval Augmented\nGeneration (RAG)\nLLM Limitation: knowledge constraints\n→ RAG: Integrating external data with LLMs\n1\nGenerated with DALL·E 3\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 13, "text": "Document loaders\nIntegrate documents with AI systems\nSupport for many common file formats\nThird party document loaders\nCSVLoader\nPyPDFLoader\nUnstructuredHTMLLoader\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 14, "text": "Loading CSV Files\nfrom langchain_community.document_loaders.csv_loader import CSVLoader\ncsv_loader = CSVLoader(file_path='path/to/your/file.csv')\ndocuments = csv_loader.load()\nprint(documents)\n[Document(page_content='Team: Nationals\\n\"Payroll (millions)\": 81.34\\n\"Wins\": 98',\nmetadata={'source': 'path/to/your/file.csv', 'row': 0}),\nDocument(page_content='Team: Reds\\n\"Payroll (millions)\": 82.20\\n\"Wins\": 97',\nmetadata={'source': 'path/to/your/file.csv', 'row': 1}),\nDocument(page_content='Team: Yankees\\n\"Payroll (millions)\": 197.96\\n\"Wins\": 95',\nmetadata={'source': 'path/to/your/file.csv', 'row': 2})]\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 15, "text": "Loading PDF Files\nfrom langchain_community.document_loaders import PyPDFLoader\npdf_loader = PyPDFLoader('rag_paper.pdf')\ndocuments = pdf_loader.load()\nprint(documents)\n[Document(page_content='Retrieval-Augmented Generation for\\nKnowledge-Intensive...',\nmetadata={'source': 'Rag Paper.pdf', 'page': 0})]\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 16, "text": "Loading HTML Files\nfrom langchain_community.document_loaders import UnstructuredHTMLLoader\nhtml_loader = UnstructuredHTMLLoader(file_path='path/to/your/file.html')\ndocuments = html_loader.load()\nfirst_document = documents[0]\nprint(\"Content:\", first_document.page_content)\nprint(\"Metadata:\", first_document.metadata)\nContent: Welcome to Our Website\nMetadata: {'source': 'path/to/your/file.html', 'section': 0}\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 18, "text": "Text splitting,\nembeddings, and\nvector storage\nR E T R I E VA L AU G M E N T E D G E N E R AT I O N ( R AG ) W I T H L A N G C H A I N\nMeri Nova\nMachine Learning Engineer"}, {"page": 24, "text": "chunk_size chunk_overlap\nInclude information beyond the boundary\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 25, "text": "CharacterTextSplitter\nfrom langchain_text_splitters import CharacterTextSplitter\ntext = \"\"\"Machine learning is a fascinating field.\\n\\nIt involves algorithms and models that\ncan learn from data. These models can then make predictions or decisions without being\nexplicitly programmed to perform the task.\\nThis capability is increasingly valuable in\nvarious industries, from finance to healthcare.\\n\\nThere are many types of machine learning,\nincluding supervised, unsupervised, and reinforcement learning.\\nEach type has its own\nstrengths and applications.\"\"\"\ntext_splitter = CharacterTextSplitter(\nseparator=\"\\n\\n\",\nchunk_size=100,\nchunk_overlap=10\n)\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 26, "text": "CharacterTextSplitter\nchunks = text_splitter.split_text(text)\nprint(chunks)\nprint([len(chunk) for chunk in chunks])\n['Machine learning is a fascinating field.',\n'It involves algorithms and models that can learn from data. These models can...',\n'There are many types of machine learning, including supervised, unsupervised...']\n[40, 260, 155]\nChunks may lack context\nChunks may be larger than chunk_size\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 27, "text": "RecursiveCharacterTextSplitter\nfrom langchain_text_splitters import RecursiveCharacterTextSplitter\nsplitter = RecursiveCharacterTextSplitter(\nseparators=[\"\\n\\n\", \"\\n\", \" \", \"\"],\nchunk_size=100,\nchunk_overlap=10\n)\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 28, "text": "RecursiveCharacterTextSplitter\nchunks = splitter.split_text(text)\nprint(chunks)\nprint([len(chunk) for chunk in chunks])\n['Machine learning is a fascinating field.',\n'It involves algorithms and models that can learn from data. These models ...',\n'or decisions without being explicitly programmed to perform the task.',\n'This capability is increasingly valuable in various industries, from ...',\n'There are many types of machine learning, including supervised, ...',\n'learning.',\n'Each type has its own strengths and applications.']\n[40, 98, 69, 91, 95, 9, 49]\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 29, "text": "Splitting documents\nfrom langchain_community.document_loaders import PyPDFLoader\nloader = PyPDFLoader(\"research_paper.pdf\")\ndocuments = loader.load()\nsplitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)\nchunks = splitter.split_documents(documents)\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 30, "text": "Splitting documents\nprint(chunks)\nprint([len(chunk.page_content) for chunk in chunks])\n[Document(metadata={'source': 'Rag Paper.pdf', 'page': 0}, page_content='...'),\nDocument(metadata={'source': 'Rag Paper.pdf', 'page': 0}, page_content='...'),\nDocument(metadata={'source': 'Rag Paper.pdf', 'page': 0}, page_content='...')]\n[928, 946, 921,...]\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 36, "text": "Embedding and storing the chunks\nEmbed and store with: OpenAI and ChromaDB\nfrom langchain_openai import OpenAIEmbeddings\nfrom langchain_chroma import Chroma\nembedding_model = OpenAIEmbeddings(\napi_key=openai_api_key,\nmodel=\"text-embedding-3-small\"\n)\nvector_store = Chroma.from_documents(\ndocuments=chunks,\nembedding=embedding_model\n)\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 38, "text": "Building an LCEL\nretrieval chain\nR E T R I E VA L AU G M E N T E D G E N E R AT I O N ( R AG ) W I T H L A N G C H A I N\nMeri Nova\nMachine Learning Engineer"}, {"page": 46, "text": "Instantiating a retriever\nvector_store = Chroma.from_documents(\ndocuments=chunks,\nembedding=embedding_model\n)\nretriever = vector_store.as_retriever(\nsearch_type=\"similarity\",\nsearch_kwargs={\"k\": 2}\n)\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 47, "text": "Creating a prompt template\nfrom langchain_core.prompts import ChatPromptTemplate\nprompt = ChatPromptTemplate.from_template(\"\"\"\nUse the following pieces of context to answer the question at the end.\nIf you don't know the answer, say that you don't know.\nContext: {context}\nQuestion: {question}\n\"\"\")\nllm = ChatOpenAI(model=\"gpt-4o-mini\", api_key=\"...\", temperature=0)\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 48, "text": "Building an LCEL retrieval chain\nfrom langchain_core.runnables import RunnablePassthrough\nfrom langchain_core.output_parsers import StrOutputParser\nchain = (\n{\"context\": retriever, \"question\": RunnablePassthrough()}\n| prompt\n| llm\n| StrOutputParser()\n)\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 49, "text": "Invoking the retrieval chain\nresult = chain.invoke({\"question\": \"What are the key findings or results presented in the paper?\"})\nprint(result)\n- Top Performance: RAG models set new records on open-domain question answering tasks...\n- Better Generation: RAG models produce more specific, diverse, and factual language...\n- Dynamic Knowledge Use: The non-parametric memory allows RAG models to access and ...\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}]}, "chapter2.pdf": {"filename": "chapter2.pdf", "total_pages": 48, "content": [{"page": 1, "text": "Loading and splitting\ncode files\nR E T R I E VA L AU G M E N T E D G E N E R AT I O N ( R AG ) W I T H L A N G C H A I N\nMeri Nova\nMachine Learning Engineer"}, {"page": 5, "text": "Loading Markdown files (.md)\nfrom langchain_community.document_loaders import UnstructuredMarkdownLoader\nloader = UnstructuredMarkdownLoader(\"README.md\")\nmarkdown_content = loader.load()\nprint(markdown_content[0])\nDocument(page_content='# Discord Text Classification ![Python Version](https...'\nmetadata={'source': 'README.md'})\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 6, "text": "Loading Python files (.py)\nfrom abc import ABC, abstractmethod from langchain_community.document_loaders \\\nimport PythonLoader\nclass LLM(ABC):\n@abstractmethod loader = PythonLoader('chatbot.py')\ndef complete_sentence(self, prompt):\npass python_data = loader.load()\nprint(python_data[0])\n...\nDocument(page_content='from abc import ABC, ..\nIntegrated into RAG applications for writing\nor fixing code, creating docs, etc.\nclass LLM(ABC):\n@abstractmethod\nImports, classes, functions, etc.\n...',\nmetadata={'source': 'chatbot.py'})\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 7, "text": "Splitting code files\npython_splitter = RecursiveCharacterTextSplitter(\nchunk_size=150, chunk_overlap=10\n)\nchunks = python_splitter.split_documents(python_data)\nfor i, chunk in enumerate(chunks[:3]):\nprint(f\"Chunk {i+1}:\\n{chunk.page_content}\\n\")\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 8, "text": "Chunk 1:\nfrom abc import ABC, abstractmethod\nclass LLM(ABC):\n@abstractmethod\ndef complete_sentence(self, prompt):\npass\nChunk 2:\nclass OpenAI(LLM):\ndef complete_sentence(self, prompt):\nreturn prompt + \" ... OpenAI end of sentence.\"\nclass Anthropic(LLM):\nChunk 3:\ndef complete_sentence(self, prompt):\nreturn prompt + \" ... Anthropic end of sentence.\"\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 9, "text": "Splitting by language\nseparators\n[\"\\n\\n\", \"\\n\", \" \", \"\"]\n[\"\\nclass \", \"\\ndef \", \"\\n\\tdef \", \"\\n\\n\", \" \", \"\"]\nfrom langchain_text_splitters import RecursiveCharacterTextSplitter, Language\npython_splitter = RecursiveCharacterTextSplitter.from_language(\nlanguage=Language.PYTHON, chunk_size=150, chunk_overlap=10\n)\nchunks = python_splitter.split_documents(data)\nfor i, chunk in enumerate(chunks[:3]):\nprint(f\"Chunk {i+1}:\\n{chunk.page_content}\\n\")\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 10, "text": "Chunk 1:\nfrom abc import ABC, abstractmethod\nChunk 2:\nclass LLM(ABC):\n@abstractmethod\ndef complete_sentence(self, prompt):\npass\nChunk 3:\nclass OpenAI(LLM):\ndef complete_sentence(self, prompt):\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 11, "text": "Let's practice!\nR E T R I E VA L AU G M E N T E D G E N E R AT I O N ( R AG ) W I T H L A N G C H A I N"}, {"page": 12, "text": "Advanced splitting\nmethods\nR E T R I E VA L AU G M E N T E D G E N E R AT I O N ( R AG ) W I T H L A N G C H A I N\nMeri Nova\nMachine Learning Engineer"}, {"page": 13, "text": "Limitations of our current splitting strategies\n1. (cid:0) Splits are naive (not context-aware) → SemanticChunker\nIgnores context of surrounding text\n2. (cid:0) Splits are made using characters vs.\ntokens → TokenTextSplitter\nTokens are processed by models\nRisk exceeding the context window\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 17, "text": "Splitting on tokens\nimport tiktoken\nfrom langchain_text_splitters import TokenTextSplitter\nexample_string = \"<PERSON> had a little lamb, it's fleece was white as snow.\"\nencoding = tiktoken.encoding_for_model('gpt-4o-mini')\nsplitter = TokenTextSplitter(encoding_name=encoding.name,\nchunk_size=10,\nchunk_overlap=2)\nchunks = splitter.split_text(example_string)\nfor i, chunk in enumerate(chunks):\nprint(f\"Chunk {i+1}:\\n{chunk}\\n\")\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 18, "text": "Splitting on tokens\nChunk 1:\n<PERSON> had a little lamb, it's fleece\nChunk 2:\nfleece was white as snow.\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 19, "text": "Splitting on tokens\nfor i, chunk in enumerate(chunks):\nprint(f\"Chunk {i+1}:\\nNo. tokens: {len(encoding.encode(chunk))}\\n{chunk}\\n\")\nChunk 1:\nNo. tokens: 10\n<PERSON> had a little lamb, it's fleece was\nChunk 2:\nNo. tokens: 6\nfleece was white as snow.\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 23, "text": "Semantic splitting\nfrom langchain_openai import OpenAIEmbeddings\nfrom langchain_experimental.text_splitter import SemanticChunker\nembeddings = OpenAIEmbeddings(api_key=\"...\", model='text-embedding-3-small')\nsemantic_splitter = SemanticChunker(\nembeddings=embeddings,\nbreakpoint_threshold_type=\"gradient\",\nbreakpoint_threshold_amount=0.8\n)\n1\nhttps://api.python.langchain.com/en/latest/text_splitter/langchain_experimental.text_splitter.\nSemanticChunker.html\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 24, "text": "Semantic splitting\nchunks = semantic_splitter.split_documents(data)\nprint(chunks[0])\npage_content='Retrieval-Augmented Generation for\\nKnowledge-Intensive NLP Tasks\\ <PERSON>,\n<PERSON>,\\<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>,<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>\\nFacebook AI\nResearch; University College London;New York University;\\<EMAIL>\\nAbstract\\nLarge\npre-trained language models have been shown to store factual knowledge\\nin their parameters,\nand achieve state-of-the-art results when ?ne-tuned on down-\\nstream NLP tasks. However, their\nability to access and precisely manipulate knowl-\\nedge is still limited, and hence on\nknowledge-intensive tasks, their performance\\nlags behind task-specific architectures.'\nmetadata={'source': 'rag_paper.pdf', 'page': 0}\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 26, "text": "Optimizing\ndocument retrieval\nR E T R I E VA L AU G M E N T E D G E N E R AT I O N ( R AG ) W I T H L A N G C H A I N\nMeri Nova\nMachine Learning Engineer"}, {"page": 28, "text": "Dense\nEncode chunks as a single vector with non-\nzero components\nPros: Capturing semantic meaning\nCons: Computationally expensive\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 29, "text": "Dense Sparse\nEncode chunks as a single vector with non- Encode using word matching with mostly zero\nzero components components\nPros: Precise, explainable, rare-word\nhandling\nPros: Capturing semantic meaning\nCons: Generalizability\nCons: Computationally expensive\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 30, "text": "Sparse retrieval methods\nTF-IDF: Encodes documents using the words that make the document unique\nBM25: Helps mitigate high-frequency words from saturating the encoding\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 31, "text": "BM25 retrieval\nfrom langchain_community.retrievers import BM25Retriever\nchunks = [\n\"Python was created by <PERSON> and released in 1991.\",\n\"Python is a popular language for machine learning (ML).\",\n\"The PyTorch library is a popular Python library for AI and ML.\"\n]\nbm25_retriever = BM25Retriever.from_texts(chunks, k=3)\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 32, "text": "BM25 retrieval\nresults = bm25_retriever.invoke(\"When was Python created?\")\nprint(\"Most Relevant Document:\")\nprint(results[0].page_content)\nMost Relevant Document:\nPython was created by <PERSON> and released in 1991.\nPython was created by <PERSON> and released in 1991.\"\n\"Python is a popular language for machine learning (ML).\"\n\"The PyTorch library is a popular Python library for AI/ML.\"\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 33, "text": "BM25 in RAG\nretriever = BM25Retriever.from_documents(\ndocuments=chunks,\nk=5\n)\nchain = ({\"context\": retriever, \"question\": RunnablePassthrough()}\n| prompt\n| llm\n| StrOutputParser()\n)\n1\nhttps://www.datacamp.com/blog/what-is-retrieval-augmented-generation-rag\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 34, "text": "BM25 in RAG\nprint(chain.invoke(\"How can LLM hallucination impact a RAG application?\"))\nThe RAG application may generate responses that are off-topic or inaccurate.\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 36, "text": "Introduction to RAG\nevaluation\nR E T R I E VA L AU G M E N T E D G E N E R AT I O N ( R AG ) W I T H L A N G C H A I N\nMeri Nova\nMachine Learning Engineer"}, {"page": 37, "text": "Types of RAG evaluation\n1\nImage Credit: LangSmith\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 38, "text": "Output accuracy: string evaluation\nquery = \"What are the main components of RAG architecture?\"\npredicted_answer = \"Training and encoding\"\nref_answer = \"Retrieval and Generation\"\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 39, "text": "Output accuracy: string evaluation\nprompt_template = \"\"\"You are an expert professor specialized in grading students' answers to q\nYou are grading the following question:{query}\nHere is the real answer:{answer}\nYou are grading the following predicted answer:{result}\nRespond with CORRECT or INCORRECT:\nGrade:\"\"\"\nprompt = PromptTemplate(\ninput_variables=[\"query\", \"answer\", \"result\"],\ntemplate=prompt_template\n)\neval_llm = ChatOpenAI(temperature=0, model=\"gpt-4o-mini\", openai_api_key='...')\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 40, "text": "Output accuracy: string evaluation\nfrom langsmith.evaluation import LangChainStringEvaluator\nqa_evaluator = LangChainStringEvaluator(\n\"qa\",\nconfig={\n\"llm\": eval_llm,\n\"prompt\": PROMPT\n}\n)\nscore = qa_evaluator.evaluator.evaluate_strings(\nprediction=predicted_answer,\nreference=ref_answer,\ninput=query\n)\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 41, "text": "Output accuracy: string evaluation\nprint(f\"Score: {score}\")\nScore: {'reasoning': 'INCORRECT', 'value': 'INCORRECT', 'score': 0}\nquery = \"What are the main components of RAG architecture?\"\npredicted_answer = \"Training and encoding\"\nref_answer = \"Retrieval and Generation\"\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 43, "text": "Faithfulness\nDoes the generated output faithfully represent the context?\nNo. of claims made that can be inferred from the context\nFaithfulness =\nTotal no. of claims\nNormalized to (0, 1)\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 44, "text": "Evaluating faithfulness\nfrom langchain_openai import ChatOpenAI, OpenAIEmbeddings\nfrom ragas.integrations.langchain import <PERSON>luatorChain\nfrom ragas.metrics import faithfulness\nllm = ChatOpenAI(model=\"gpt-4o-mini\", api_key=\"...\")\nembeddings = OpenAIEmbeddings(model=\"text-embedding-3-small\", api_key=\"...\")\nfaithfulness_chain = EvaluatorChain(\nmetric=faithfulness,\nllm=llm,\nembeddings=embeddings\n)\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 45, "text": "Evaluating faithfulness\neval_result = faithfulness_chain({\n\"question\": \"How does the RAG model improve question answering with LLMs?\",\n\"answer\": \"The RAG model improves question answering by combining the retrieval of documents...\",\n\"contexts\": [\n\"The RAG model integrates document retrieval with LLMs by first retrieving relevant passages...\",\n\"By incorporating retrieval mechanisms, RAG leverages external knowledge sources, allowing the...\",\n]\n})\nprint(eval_result)\n'faithfulness': 1.0\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 46, "text": "Context precision\nHow relevant are the retrieved documents to the query?\nNormalized to (0, 1) → 1 = highly relevant\nfrom ragas.metrics import context_precision\nllm = ChatOpenAI(model=\"gpt-4o-mini\", api_key=\"...\")\nembeddings = OpenAIEmbeddings(model=\"text-embedding-3-small\", api_key=\"...\")\ncontext_precision_chain = EvaluatorChain(\nmetric=context_precision,\nllm=llm,\nembeddings=embeddings\n)\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 47, "text": "Evaluating context precision\neval_result = context_precision_chain({\n\"question\": \"How does the RAG model improve question answering with large language models?\",\n\"ground_truth\": \"The RAG model improves question answering by combining the retrieval of...\",\n\"contexts\": [\n\"The RAG model integrates document retrieval with LLMs by first retrieving...\",\n\"By incorporating retrieval mechanisms, RAG leverages external knowledge sources...\",\n]\n})\nprint(f\"Context Precision: {eval_result['context_precision']}\")\nContext Precision: 0.99999999995\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}]}, "chapter3.pdf": {"filename": "chapter3.pdf", "total_pages": 61, "content": [{"page": 1, "text": "From vectors to\ngraphs\nR E T R I E VA L AU G M E N T E D G E N E R AT I O N ( R AG ) W I T H L A N G C H A I N\nMeri Nova\nMachine Learning Engineer"}, {"page": 12, "text": "Loading and chunking Wikipedia pages\nfrom langchain_community.document_loaders import WikipediaLoader\nfrom langchain_text_splitters import TokenTextSplitter\nraw_documents = WikipediaLoader(query=\"large language model\").load()\ntext_splitter = TokenTextSplitter(chunk_size=100, chunk_overlap=20)\ndocuments = text_splitter.split_documents(raw_documents[:3])\nprint(documents[0])\npage_content='A large language model (LLM) is a computational model capable of...'\nmetadata={'title': 'Large language model',\n'summary': \"A large language model (LLM) is...\",\n'source': 'https://en.wikipedia.org/wiki/Large_language_model'}\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 13, "text": "From text to graphs!\nfrom langchain_openai import ChatOpenAI\nfrom langchain_experimental.graph_transformers import LLMGraphTransformer\nllm = ChatOpenAI(api_key=\"...\", temperature=0, model_name=\"gpt-4o-mini\")\nllm_transformer = LLMGraphTransformer(llm=llm)\ngraph_documents = llm_transformer.convert_to_graph_documents(documents)\nprint(graph_documents)\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 14, "text": "From text to graphs!\n[GraphDocument(\nnodes=[\nNode(id='Llm', type='Computational model'),\nNode(id='Language Generation', type='Concept'),\nNode(id='Natural Language Processing Tasks', type='Concept'),\nNode(id='Llama Family', type='Computational model'),\nNode(id='Ibm', type='Organization'),\n..., Node(id='Bert', type='Computational model')],\nrelationships=[\nRelationship(source=Node(id='Llm', type='Computational model'),\ntarget=Node(id='Language Generation', type='Concept'),\ntype='CAPABLE_OF'),\n...])]\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 15, "text": "Let's practice!\nR E T R I E VA L AU G M E N T E D G E N E R AT I O N ( R AG ) W I T H L A N G C H A I N"}, {"page": 17, "text": "Instantiating the Neo4j database\nfrom langchain_community.graphs import Neo4jGraph\ngraph = Neo4jGraph(url=\"bolt://localhost:7687\", username=\"neo4j\", password=\"...\")\nimport os\nurl = os.environ[\"NEO4J_URI\"]\nuser = os.environ[\"NEO4J_USERNAME\"]\npassword = os.environ[\"NEO4J_PASSWORD\"]\ngraph = Neo4jGraph(url=url, username=user, password=password)\n1\nhttps://neo4j.com/download/\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 18, "text": "Storing graph documents\nfrom langchain_experimental.graph_transformers import LLMGraphTransformer\nllm = ChatOpenAI(api_key=\"...\", temperature=0, model=\"gpt-4o-mini\")\nllm_transformer = LLMGraphTransformer(llm=llm)\ngraph_documents = llm_transformer.convert_to_graph_documents(documents)\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 19, "text": "Storing graph documents\ngraph.add_graph_documents(\ngraph_documents,\ninclude_source=True,\nbaseEntityLabel=True\n)\ninclude_source=True : link nodes to source documents with MENTIONS edge\nbaseEntityLabel=True : add __Entity__ label to each node\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 22, "text": "Database schema\nprint(graph.get_schema)\nNode properties:\nConcept {id: STRING}\nArchitecture {id: STRING}\nOrganization {id: STRING}\nEvent {id: STRING}\nPaper {id: STRING}\nThe relationships:\n(:Concept)-[:DEVELOPED_BY]->(:Person)\n(:Architecture)-[:BASED_ON]->(:Concept)\n(:Organization)-[:PROPOSED]->(:Concept)\n(:Document)-[:MENTIONS]->(:Event)\n(:Paper)-[:BASED_ON]->(:Concept)\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 28, "text": "Querying the LLM graph\nresults = graph.query(\"\"\"\nMATCH (gpt4:Model {id: \"Gpt-4\"})-[:DEVELOPED_BY]->(org:Organization)\nRETURN org\n\"\"\")\nprint(results)\n[{'org': {'id': 'Openai'}}]\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 30, "text": "Creating the Graph\nRAG chain\nR E T R I E VA L AU G M E N T E D G E N E R AT I O N ( R AG ) W I T H L A N G C H A I N\nMeri Nova\nMachine Learning Engineer"}, {"page": 40, "text": "Refresh schema\ngraph.refresh_schema()\nprint(graph.get_schema)\nNode properties:\nDocument {title: STRING, id: STRING, text: STRING, summary: STRING, source: STRING}\nConcept {id: STRING}\nOrganization {id: STRING}\nRelationship properties:\nThe relationships:\n(:Document)-[:MENTIONS]->(:Organization)\n(:Concept)-[:DEVELOPED_BY]->(:Person)\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 41, "text": "Querying the graph\nfrom langchain_community.chains.graph_qa.cypher import GraphCypherQAChain\nchain = GraphCypherQAChain.from_llm(\nllm=ChatOpenAI(api_key=\"...\", temperature=0), graph=graph, verbose=True\n)\nresult = chain.invoke({\"query\": \"What is the most accurate model?\"})\n1\nhttps://api.python.langchain.com/en/latest/chains/langchain_community.chains.graph_qa.cypher.\nGraphCypherQAChain.html\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 42, "text": "Querying the graph\nprint(f\"Final answer: {result['result']}\")\n> Entering new GraphCypherQAChain chain...\nGenerated Cypher:\nMATCH (m:Model)\nRETURN m\nORDER BY m.accuracy DESC\nLIMIT 1;\nFull Context:\n[{'m': {'id': 'Artificial Neural Networks'}}]\n> Finished chain.\nFinal answer: Artificial Neural Networks\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 43, "text": "Customization\nchain = GraphCypherQAChain.from_llm(\nllm=ChatOpenAI(api_key=\"...\", temperature=0), graph=graph, verbose=True\n)\nqa_prompt : Prompt template for result generation\ncypher_prompt : Prompt template for Cypher generation\ncypher_llm : LLM for Cypher generation\nqa_llm : LLM for result generation\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 45, "text": "Improving graph\nretrieval\nR E T R I E VA L AU G M E N T E D G E N E R AT I O N ( R AG ) W I T H L A N G C H A I N\nMeri Nova\nMachine Learning Engineer"}, {"page": 46, "text": "Techniques\nMain limitation: reliability of user → Cypher translation\nStrategies to improve graph retrieval system:\nFiltering Graph Schema\nValidating the Cypher Query\nFew-shot prompting\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 47, "text": "Filtering\nfrom langchain_community.chains.graph_qa.cypher import GraphCypherQAChain\nllm = ChatOpenAI(api_key=\"...\", model=\"gpt-4o-mini\", temperature=0)\nchain = GraphCypherQAChain.from_llm(\ngraph=graph, llm=llm, exclude_types=[\"Concept\"], verbose=True\n)\nprint(graph.get_schema)\nNode properties:\nDocument {title: STRING, id: STRING, text: STRING, summary: STRING, source: STRING}\nOrganization {id: STRING}\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 48, "text": "Validating the Cypher query\nDifficulty in interpreting the direction of relationships\nchain = GraphCypherQAChain.from_llm(\ngraph=graph, llm=llm, verbose=True, validate_cypher=True\n)\n1. Detects nodes and relationships\n2. Determines the directions of the relationship\n3. Checks the graph schema\n4. Update the direction of relationships\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 49, "text": "Few-shot prompting\nexamples = [\n{\n\"question\": \"How many notable large language models are mentioned in the article?\",\n\"query\": \"MATCH (m:Concept {id: 'Large Language Model'}) RETURN count(DISTINCT m)\",\n},\n{\n\"question\": \"Which companies or organizations have developed the large language models mentioned?\",\n\"query\": \"MATCH (o:Organization)-[:DEVELOPS]->(m:Concept {id: 'Large Language Model'}) RETURN DISTINCT o.id\",\n},\n{\n\"question\": \"What is the largest model size mentioned in the article, in terms of number of parameters?\",\n\"query\": \"MATCH (m:Concept {id: 'Large Language Model'}) RETURN max(m.parameters) AS largest_model\",\n},\n]\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 50, "text": "Implementing few-shot prompting\nfrom langchain_core.prompts import FewShotPromptTemplate, PromptTemplate\nexample_prompt = PromptTemplate.from_template(\n\"User input: {question}\\nCypher query: {query}\"\n)\ncypher_prompt = FewShotPromptTemplate(\nexamples=examples,\nexample_prompt=example_prompt,\nprefix=\"You are a Neo4j expert. Given an input question, create a syntactically correct\nCypher query to run.\\n\\nHere is the schema information\\n{schema}.\\n\\n\nBelow are a number of examples of questions and their corresponding Cypher queries.\",\nsuffix=\"User input: {question}\\nCypher query: \",\ninput_variables=[\"question\"],\n)\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 51, "text": "Complete prompt\nYou are a Neo4j expert. Given an input question, create a syntactically correct Cypher query to run.\nBelow are a number of examples of questions and their corresponding Cypher queries.\nUser input: How many notable large language models are mentioned in the article?\nCypher query: MATCH (p:Paper) RETURN count(DISTINCT p)\nUser input: Which companies or organizations have developed the large language models?\nCypher query: MATCH (o:Organization)-[:DEVELOPS]->(m:Concept {id: 'Large Language Model'}) RETURN DISTINCT o.id\nUser input: What is the largest model size mentioned in the article, in terms of number of parameters?\nCypher query: MATCH (m:Concept {id: 'Large Language Model'}) RETURN max(m.parameters) AS largest_model\nUser input: How many papers were published in 2016?\nCypher query:\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 52, "text": "Adding the few-shot examples\nchain = GraphCypherQAChain.from_llm(\ngraph=graph, llm=llm, cypher_prompt=cypher_prompt,\nverbose=True, validate_cypher=True\n)\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}, {"page": 57, "text": "Chapter 2\nMarkdown files Token splitting →\nTokenTextSplitter\nUnstructuredMarkdownLoader\nSemantic splitting →\nSemanticChunker\nPython files\nPythonLoader\nlanguage=Language.PYTHON\nRETRIEVAL AUGMENTED GENERATION (RAG) WITH LANGCHAIN"}]}}